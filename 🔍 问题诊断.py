#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
问题诊断脚本
帮助找出脚本执行失败的原因
"""

import sys
import os
from pathlib import Path
import subprocess

def check_python_environment():
    """检查Python环境"""
    print("="*60)
    print("1. Python环境检查")
    print("="*60)
    
    print(f"Python版本: {sys.version}")
    print(f"Python路径: {sys.executable}")
    print(f"当前工作目录: {os.getcwd()}")
    
    # 检查依赖包
    packages = ["tushare", "pandas", "sqlalchemy", "pymysql", "numpy"]
    missing = []
    
    print(f"\n依赖包检查:")
    for package in packages:
        try:
            __import__(package)
            print(f"✓ {package}")
        except ImportError:
            print(f"✗ {package} (缺失)")
            missing.append(package)
    
    if missing:
        print(f"\n缺少的包: {', '.join(missing)}")
        print(f"安装命令: pip install {' '.join(missing)}")
        return False
    
    return True

def check_file_structure():
    """检查文件结构"""
    print(f"\n{'='*60}")
    print("2. 文件结构检查")
    print("="*60)
    
    current_dir = Path.cwd()
    print(f"当前目录: {current_dir}")
    
    # 检查目录结构
    expected_dirs = [
        "01-主要工具",
        "02-数据导出工具", 
        "03-原始数据脚本",
        "04-文档说明",
        "05-日志记录"
    ]
    
    print(f"\n目录检查:")
    for dir_name in expected_dirs:
        dir_path = current_dir / dir_name
        if dir_path.exists():
            print(f"✓ {dir_name}")
        else:
            print(f"✗ {dir_name} (不存在)")
    
    # 检查原始数据脚本
    scripts_dir = current_dir / "03-原始数据脚本"
    expected_scripts = [
        "全量股票日线123.py",
        "指数全量.py",
        "tushare_index_data.py",
        "财务数据分开表格全量更新.py",
        "分红数据分开表格全量更新.py"
    ]
    
    print(f"\n原始数据脚本检查:")
    if scripts_dir.exists():
        for script in expected_scripts:
            script_path = scripts_dir / script
            if script_path.exists():
                print(f"✓ {script}")
            else:
                print(f"✗ {script} (不存在)")
                # 尝试在其他位置找
                for possible_dir in [current_dir, current_dir.parent]:
                    alt_path = possible_dir / script
                    if alt_path.exists():
                        print(f"  → 找到在: {alt_path}")
    else:
        print("03-原始数据脚本 目录不存在")
        # 尝试在当前目录找脚本
        print("在当前目录查找脚本:")
        for script in expected_scripts:
            script_path = current_dir / script
            if script_path.exists():
                print(f"✓ {script} (在根目录)")

def check_database_connection():
    """检查数据库连接"""
    print(f"\n{'='*60}")
    print("3. 数据库连接检查")
    print("="*60)
    
    try:
        import pymysql
        
        # 尝试连接数据库
        connection = pymysql.connect(
            host="localhost",
            port=3306,
            user="root",
            password="12345678",
            charset="utf8mb4",
            connect_timeout=5
        )
        
        cursor = connection.cursor()
        cursor.execute("SELECT VERSION()")
        version = cursor.fetchone()
        connection.close()
        
        print(f"✓ MySQL连接成功")
        print(f"  版本: {version[0]}")
        return True
        
    except Exception as e:
        print(f"✗ MySQL连接失败: {e}")
        print("  可能的原因:")
        print("  - MySQL服务未启动")
        print("  - 用户名或密码错误")
        print("  - 端口3306被占用")
        return False

def check_tushare_api():
    """检查Tushare API"""
    print(f"\n{'='*60}")
    print("4. Tushare API检查")
    print("="*60)
    
    try:
        import tushare as ts
        
        # 设置token
        ts.set_token("2876ea85cb005fb5fa17c809a98174f2d5aae8b1f830110a5ead6211")
        pro = ts.pro_api()
        
        # 测试API调用
        df = pro.stock_basic(exchange='', list_status='L', fields='ts_code,symbol,name', limit=5)
        
        if not df.empty:
            print(f"✓ Tushare API连接成功")
            print(f"  测试获取到 {len(df)} 条股票信息")
            return True
        else:
            print("✗ Tushare API返回空数据")
            return False
            
    except Exception as e:
        print(f"✗ Tushare API测试失败: {e}")
        return False

def generate_diagnosis_report():
    """生成诊断报告"""
    print(f"\n{'='*60}")
    print("诊断报告")
    print("="*60)
    
    # 运行所有检查
    checks = {
        "Python环境": check_python_environment(),
        "数据库连接": check_database_connection(), 
        "Tushare API": check_tushare_api()
    }
    
    # 文件结构检查（不返回布尔值）
    check_file_structure()
    
    print(f"\n检查结果汇总:")
    for check_name, result in checks.items():
        status = "✓ 通过" if result else "✗ 失败"
        print(f"  {check_name}: {status}")
    
    # 给出建议
    print(f"\n建议:")
    if not checks["Python环境"]:
        print("1. 安装缺失的Python包")
    if not checks["数据库连接"]:
        print("2. 检查MySQL服务状态")
    if not checks["Tushare API"]:
        print("3. 检查网络连接和API配置")
    
    # 检查脚本文件
    scripts_dir = Path.cwd() / "03-原始数据脚本"
    if not scripts_dir.exists():
        print("4. 脚本文件可能未正确分类，建议检查文件位置")

def main():
    """主函数"""
    print("问题诊断工具")
    print("帮助找出脚本执行失败的原因")
    
    generate_diagnosis_report()
    
    print(f"\n{'='*60}")
    print("诊断完成")
    print("="*60)
    print("请根据上述检查结果解决相应问题")

if __name__ == "__main__":
    main()
    input("\n按回车键退出...")
