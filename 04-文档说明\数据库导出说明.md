# 数据库导出工具使用说明

## 概述

本工具用于将MySQL数据库中的所有数据导出为CSV文件，方便数据备份、分析和迁移。

## 导出目标

**导出目录**: `K:\数据库存储地`

**目标数据库**:
- `Daily Line` - 股票日线数据
- `index` - 指数数据  
- `qtdb` - 财务和分红数据
- `ind` - Tushare指数数据

## 文件说明

### 主要工具
- `启动数据库导出.bat` - 一键启动工具（推荐使用）
- `快速预览导出.py` - 预览导出格式
- `批量导出所有数据.py` - 批量导出所有数据
- `数据库导出工具.py` - 完整功能的导出工具

### 测试工具
- `简单导出测试.py` - 基础功能测试

## 使用方法

### 方法1：使用批处理文件（推荐）
1. 双击运行 `启动数据库导出.bat`
2. 选择操作：
   - 选择 `1` - 快速预览（先查看导出格式）
   - 选择 `2` - 批量导出所有数据

### 方法2：直接运行Python脚本
```bash
# 预览导出格式
py "快速预览导出.py"

# 批量导出所有数据
py "批量导出所有数据.py"
```

## 导出格式

### 文件格式
- **主要格式**: CSV文件（UTF-8编码，带BOM）
- **辅助格式**: Excel文件（仅小表，行数≤10万）

### 文件组织结构
```
K:\数据库存储地\
├── Daily Line\
│   ├── stock_daily_000001.csv
│   ├── stock_daily_000002.csv
│   └── ...
├── index\
│   ├── 000001sh.csv
│   ├── 000016sh.csv
│   └── ...
├── qtdb\
│   ├── balance_sheet_000001_sz\
│   │   ├── balance_sheet_000001_sz_part_0001.csv
│   │   └── balance_sheet_000001_sz_part_0002.csv
│   └── ...
├── ind\
│   └── ...
└── 导出汇总报告.json
```

### 大表处理
- **小表**（≤5万行）：直接导出为单个CSV文件
- **大表**（>5万行）：分块导出，每块5万行
- 分块文件命名：`表名_part_0001.csv`, `表名_part_0002.csv` ...

## 数据样本

### 股票日线数据样本
```csv
ts_code,trade_date,open,high,low,close,pre_close,change,pct_chg,vol,amount,turnover_rate,pe,pb,...
000001.SZ,20250808,12.48,12.53,12.39,12.4,12.47,-0.07,-0.5613,829795.91,1032586.759,0.4276,5.4065,0.5607,...
```

### 指数数据样本
```csv
candle_end_time,open,high,low,close,amount,volume,index_code
2025-08-08,3634.8546,3645.3667,3625.4508,3635.1281,713569731.1,526407572.0,000001.SH
```

## 系统要求

### 软件要求
- Python 3.6+
- MySQL 5.7+
- 足够的磁盘空间（建议至少10GB）

### Python依赖
- pandas
- pymysql
- openpyxl（用于Excel导出）

### 数据库配置
- 主机: localhost:3306
- 用户: root
- 密码: 12345678

## 预计时间和空间

### 导出时间
- **预览导出**: 1-2分钟
- **完整导出**: 1-3小时（取决于数据量）

### 磁盘空间需求
- **股票日线数据**: 约2-5GB
- **财务数据**: 约1-3GB  
- **指数数据**: 约100MB
- **分红数据**: 约500MB
- **总计**: 约5-10GB

## 注意事项

### 执行前检查
1. **MySQL服务**: 确保MySQL服务正在运行
2. **磁盘空间**: 确保K盘有足够空间
3. **网络稳定**: 避免在导出过程中断网
4. **系统资源**: 避免同时运行其他大型程序

### 执行过程中
1. **不要中断**: 避免强制关闭程序
2. **监控进度**: 观察控制台输出了解进度
3. **检查错误**: 如有错误及时记录

### 执行后检查
1. **查看日志**: 检查是否有导出失败的表
2. **验证文件**: 随机检查几个文件确认数据完整性
3. **查看汇总**: 检查 `导出汇总报告.json` 了解详细统计

## 故障排除

### 常见问题

#### 1. 数据库连接失败
- 检查MySQL服务是否启动
- 验证用户名密码是否正确
- 确认端口3306是否开放

#### 2. 磁盘空间不足
- 清理K盘空间
- 或修改脚本中的导出路径

#### 3. 导出中断
- 重新运行脚本，会跳过已存在的文件
- 或删除部分导出的文件重新开始

#### 4. 文件编码问题
- CSV文件使用UTF-8编码（带BOM）
- 可用Excel或记事本正常打开

### 错误日志
导出过程中的错误信息会显示在控制台，建议截图保存以便排查问题。

## 高级用法

### 修改导出配置
编辑脚本中的配置部分：
```python
# 修改导出目录
EXPORT_DIR = Path(r"D:\数据备份")

# 修改数据库配置
MYSQL_CONFIG = {
    "host": "localhost",
    "port": 3306,
    "user": "your_username",
    "password": "your_password",
    "charset": "utf8mb4"
}

# 修改分块大小
chunk_size = 100000  # 每块10万行
```

### 选择性导出
如只需要导出特定数据库，可修改 `DATABASES` 列表：
```python
DATABASES = ["Daily Line"]  # 只导出股票数据
```

## 技术支持

如遇问题，请提供：
1. 错误信息截图
2. 系统环境信息
3. 数据库状态
4. 磁盘空间情况
