@echo off
chcp 65001 >nul
title 路径测试

echo 测试分类后的文件路径
echo ========================

echo.
echo 当前目录: %CD%
echo.

echo 检查目录结构:
if exist "01-主要工具" (
    echo ✓ 01-主要工具 目录存在
) else (
    echo ✗ 01-主要工具 目录不存在
)

if exist "02-数据导出工具" (
    echo ✓ 02-数据导出工具 目录存在
) else (
    echo ✗ 02-数据导出工具 目录不存在
)

if exist "03-原始数据脚本" (
    echo ✓ 03-原始数据脚本 目录存在
) else (
    echo ✗ 03-原始数据脚本 目录不存在
)

echo.
echo 检查关键文件:
if exist "01-主要工具\系统检查.py" (
    echo ✓ 系统检查.py 存在
) else (
    echo ✗ 系统检查.py 不存在
)

if exist "01-主要工具\一键启动全量数据获取_最终版.py" (
    echo ✓ 一键启动全量数据获取_最终版.py 存在
) else (
    echo ✗ 一键启动全量数据获取_最终版.py 不存在
)

if exist "02-数据导出工具\快速预览导出.py" (
    echo ✓ 快速预览导出.py 存在
) else (
    echo ✗ 快速预览导出.py 不存在
)

echo.
echo 测试系统检查脚本:
cd /d "01-主要工具"
echo 当前目录: %CD%
py -c "print('Python可以运行')"
cd /d ..

echo.
echo 路径测试完成
pause
