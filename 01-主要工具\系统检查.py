#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
系统检查脚本
检查系统是否准备就绪运行数据获取脚本
"""

import sys
import subprocess
from pathlib import Path

def check_python():
    """检查Python环境"""
    print("1. Python环境检查")
    print("-" * 40)
    print(f"Python版本: {sys.version}")
    print(f"Python路径: {sys.executable}")

    if sys.version_info >= (3, 6):
        print("✓ Python版本符合要求")
        return True
    else:
        print("✗ Python版本过低，需要3.6+")
        return False

def check_packages():
    """检查依赖包"""
    print("\n2. 依赖包检查")
    print("-" * 40)

    packages = {
        "tushare": "Tushare数据接口",
        "pandas": "数据处理",
        "sqlalchemy": "数据库ORM",
        "pymysql": "MySQL连接器",
        "numpy": "数值计算"
    }

    missing = []
    for package, desc in packages.items():
        try:
            __import__(package)
            print(f"✓ {package} - {desc}")
        except ImportError:
            print(f"✗ {package} - {desc} (未安装)")
            missing.append(package)

    if missing:
        print(f"\n缺少依赖包: {', '.join(missing)}")
        print(f"安装命令: pip install {' '.join(missing)}")
        return False

    print("✓ 所有依赖包已安装")
    return True

def check_database():
    """检查数据库连接"""
    print("\n3. 数据库连接检查")
    print("-" * 40)

    try:
        import pymysql
        connection = pymysql.connect(
            host="localhost",
            port=3306,
            user="root",
            password="12345678",
            charset="utf8mb4",
            connect_timeout=5
        )

        cursor = connection.cursor()
        cursor.execute("SELECT VERSION()")
        version = cursor.fetchone()
        connection.close()

        print(f"✓ MySQL连接成功")
        print(f"  版本: {version[0]}")
        print(f"  主机: localhost:3306")
        print(f"  用户: root")
        return True

    except Exception as e:
        print(f"✗ MySQL连接失败: {e}")
        print("  请检查:")
        print("  - MySQL服务是否启动")
        print("  - 用户名密码是否正确")
        print("  - 端口3306是否开放")
        return False

def check_scripts():
    """检查脚本文件"""
    print("\n4. 脚本文件检查")
    print("-" * 40)

    scripts = [
        "全量股票日线123.py",
        "指数全量.py",
        "tushare_index_data.py",
        "财务数据分开表格全量更新.py",
        "分红数据分开表格全量更新.py"
    ]

    script_dir = Path("../03-原始数据脚本")
    missing = []
    for script in scripts:
        script_path = script_dir / script
        if script_path.exists():
            print(f"✓ {script}")
        else:
            print(f"✗ {script} (文件不存在)")
            missing.append(script)

    if missing:
        print(f"\n缺少脚本文件: {len(missing)} 个")
        return False

    print("✓ 所有脚本文件存在")
    return True

def check_tushare_token():
    """检查Tushare token"""
    print("\n5. Tushare API检查")
    print("-" * 40)

    try:
        import tushare as ts
        ts.set_token("2876ea85cb005fb5fa17c809a98174f2d5aae8b1f830110a5ead6211")
        pro = ts.pro_api()

        # 测试API调用
        df = pro.stock_basic(exchange='', list_status='L', fields='ts_code,symbol,name')
        if not df.empty:
            print(f"✓ Tushare API连接成功")
            print(f"  获取到 {len(df)} 只股票信息")
            return True
        else:
            print("✗ Tushare API返回空数据")
            return False

    except Exception as e:
        print(f"✗ Tushare API测试失败: {e}")
        print("  请检查:")
        print("  - 网络连接是否正常")
        print("  - Token是否有效")
        return False

def main():
    """主函数"""
    print("="*60)
    print("一键启动全量数据获取系统 - 系统检查")
    print("="*60)

    checks = [
        check_python(),
        check_packages(),
        check_database(),
        check_scripts(),
        check_tushare_token()
    ]

    passed = sum(checks)
    total = len(checks)

    print("\n" + "="*60)
    print("检查结果汇总")
    print("="*60)
    print(f"通过检查: {passed}/{total}")

    if passed == total:
        print("✓ 系统准备就绪，可以运行数据获取脚本")
        print("\n推荐运行方式:")
        print("1. 双击 '启动全量数据获取_最终版.bat'")
        print("2. 或运行: py \"一键启动全量数据获取_最终版.py\"")
        return True
    else:
        print("✗ 系统未准备就绪，请解决上述问题后重试")
        return False

if __name__ == "__main__":
    success = main()
    input("\n按回车键退出...")
    sys.exit(0 if success else 1)
# -*- coding: utf-8 -*-
"""
系统检查脚本
检查系统是否准备就绪运行数据获取脚本
"""

import sys
import subprocess
from pathlib import Path

def check_python():
    """检查Python环境"""
    print("1. Python环境检查")
    print("-" * 40)
    print(f"Python版本: {sys.version}")
    print(f"Python路径: {sys.executable}")
    
    if sys.version_info >= (3, 6):
        print("✓ Python版本符合要求")
        return True
    else:
        print("✗ Python版本过低，需要3.6+")
        return False

def check_packages():
    """检查依赖包"""
    print("\n2. 依赖包检查")
    print("-" * 40)
    
    packages = {
        "tushare": "Tushare数据接口",
        "pandas": "数据处理",
        "sqlalchemy": "数据库ORM",
        "pymysql": "MySQL连接器",
        "numpy": "数值计算"
    }
    
    missing = []
    for package, desc in packages.items():
        try:
            __import__(package)
            print(f"✓ {package} - {desc}")
        except ImportError:
            print(f"✗ {package} - {desc} (未安装)")
            missing.append(package)
    
    if missing:
        print(f"\n缺少依赖包: {', '.join(missing)}")
        print(f"安装命令: pip install {' '.join(missing)}")
        return False
    
    print("✓ 所有依赖包已安装")
    return True

def check_database():
    """检查数据库连接"""
    print("\n3. 数据库连接检查")
    print("-" * 40)
    
    try:
        import pymysql
        connection = pymysql.connect(
            host="localhost",
            port=3306,
            user="root",
            password="12345678",
            charset="utf8mb4",
            connect_timeout=5
        )
        
        cursor = connection.cursor()
        cursor.execute("SELECT VERSION()")
        version = cursor.fetchone()
        connection.close()
        
        print(f"✓ MySQL连接成功")
        print(f"  版本: {version[0]}")
        print(f"  主机: localhost:3306")
        print(f"  用户: root")
        return True
        
    except Exception as e:
        print(f"✗ MySQL连接失败: {e}")
        print("  请检查:")
        print("  - MySQL服务是否启动")
        print("  - 用户名密码是否正确")
        print("  - 端口3306是否开放")
        return False

def check_scripts():
    """检查脚本文件"""
    print("\n4. 脚本文件检查")
    print("-" * 40)
    
    scripts = [
        "全量股票日线123.py",
        "指数全量.py",
        "tushare_index_data.py",
        "财务数据分开表格全量更新.py",
        "分红数据分开表格全量更新.py"
    ]
    
    missing = []
    for script in scripts:
        if Path(script).exists():
            print(f"✓ {script}")
        else:
            print(f"✗ {script} (文件不存在)")
            missing.append(script)
    
    if missing:
        print(f"\n缺少脚本文件: {len(missing)} 个")
        return False
    
    print("✓ 所有脚本文件存在")
    return True

def check_tushare_token():
    """检查Tushare token"""
    print("\n5. Tushare API检查")
    print("-" * 40)
    
    try:
        import tushare as ts
        ts.set_token("2876ea85cb005fb5fa17c809a98174f2d5aae8b1f830110a5ead6211")
        pro = ts.pro_api()
        
        # 测试API调用
        df = pro.stock_basic(exchange='', list_status='L', fields='ts_code,symbol,name')
        if not df.empty:
            print(f"✓ Tushare API连接成功")
            print(f"  获取到 {len(df)} 只股票信息")
            return True
        else:
            print("✗ Tushare API返回空数据")
            return False
            
    except Exception as e:
        print(f"✗ Tushare API测试失败: {e}")
        print("  请检查:")
        print("  - 网络连接是否正常")
        print("  - Token是否有效")
        return False

def main():
    """主函数"""
    print("="*60)
    print("一键启动全量数据获取系统 - 系统检查")
    print("="*60)
    
    checks = [
        check_python(),
        check_packages(),
        check_database(),
        check_scripts(),
        check_tushare_token()
    ]
    
    passed = sum(checks)
    total = len(checks)
    
    print("\n" + "="*60)
    print("检查结果汇总")
    print("="*60)
    print(f"通过检查: {passed}/{total}")
    
    if passed == total:
        print("✓ 系统准备就绪，可以运行数据获取脚本")
        print("\n推荐运行方式:")
        print("1. 双击 '启动全量数据获取_最终版.bat'")
        print("2. 或运行: py \"一键启动全量数据获取_最终版.py\"")
        return True
    else:
        print("✗ 系统未准备就绪，请解决上述问题后重试")
        return False

if __name__ == "__main__":
    success = main()
    input("\n按回车键退出...")
    sys.exit(0 if success else 1)
