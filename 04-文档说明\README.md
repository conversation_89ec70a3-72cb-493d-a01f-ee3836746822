# 一键启动全量数据获取系统

## 概述

本系统整合了多个数据获取脚本，可以一键启动获取股票、指数、财务和分红数据。

## 系统要求

### 软件要求
- Python 3.6+
- MySQL 5.7+ 或 MariaDB 10.2+

### Python依赖包
- tushare
- pandas
- sqlalchemy
- pymysql
- numpy

## 安装依赖

```bash
pip install tushare pandas sqlalchemy pymysql numpy
```

或使用国内镜像：
```bash
pip install tushare pandas sqlalchemy pymysql numpy -i https://pypi.tuna.tsinghua.edu.cn/simple
```

## 数据库配置

### MySQL配置
- 主机: localhost
- 端口: 3306
- 用户: root
- 密码: 12345678

### 数据库列表
系统会自动创建以下数据库：
- `Daily Line` - 股票日线数据
- `index` - 指数数据
- `qtdb` - 财务和分红数据
- `ind` - Tushare指数数据

## 使用方法

### 方法1：使用批处理文件（推荐）
双击运行 `启动全量数据获取_最终版.bat`

### 方法2：使用Python脚本
```bash
py "一键启动全量数据获取_最终版.py"
```

### 方法3：手动运行单个脚本
```bash
py "全量股票日线123.py"
py "指数全量.py"
py "tushare_index_data.py"
py "财务数据分开表格全量更新.py"
py "分红数据分开表格全量更新.py"
```

## 脚本说明

### 1. 全量股票日线123.py
- 功能：获取所有股票的日线数据和基本指标
- 数据库：Daily Line
- 预计时间：2-3小时
- 表结构：每只股票一个表 (stock_daily_XXXXXX)

### 2. 指数全量.py
- 功能：获取主要指数的历史数据
- 数据库：index
- 预计时间：30分钟
- 包含指数：上证指数、深证成指、沪深300、中证500等

### 3. tushare_index_data.py
- 功能：获取指数数据和成分股信息
- 数据库：ind
- 预计时间：30分钟

### 4. 财务数据分开表格全量更新.py
- 功能：获取所有股票的财务数据（资产负债表、利润表、现金流量表）
- 数据库：qtdb
- 预计时间：3-4小时
- 表结构：每只股票三个表

### 5. 分红数据分开表格全量更新.py
- 功能：获取所有股票的分红数据
- 数据库：qtdb
- 预计时间：1小时
- 表结构：每只股票一个表

## 注意事项

1. **MySQL服务**：确保MySQL服务已启动
2. **网络连接**：需要稳定的网络连接访问Tushare API
3. **API限制**：Tushare有API调用频率限制，脚本已内置限流机制
4. **执行时间**：完整执行预计需要6-8小时
5. **磁盘空间**：确保有足够的磁盘空间存储数据
6. **中断恢复**：如果执行中断，可以重新运行，脚本会跳过已存在的数据

## 故障排除

### 1. Python环境问题
```bash
# 检查Python版本
py --version

# 检查包安装
py -c "import tushare, pandas, sqlalchemy, pymysql, numpy; print('All packages OK')"
```

### 2. 数据库连接问题
- 检查MySQL服务是否启动
- 验证用户名密码是否正确
- 确认端口3306是否开放

### 3. API调用问题
- 检查网络连接
- 验证Tushare token是否有效
- 查看API调用频率是否超限

### 4. 磁盘空间问题
- 检查可用磁盘空间
- 清理临时文件

## 日志文件

执行过程中会生成日志文件在 `logs` 目录下，文件名格式：
`全量数据获取_YYYYMMDD_HHMMSS.log`

## 配置修改

如需修改数据库配置，请编辑脚本中的CONFIG部分：

```python
CONFIG = {
    "TUSHARE_TOKEN": "your_token_here",
    "MYSQL_CONFIG": {
        "host": "localhost",
        "port": 3306,
        "user": "root",
        "password": "your_password",
        "charset": "utf8mb4"
    }
}
```

## 技术支持

如遇问题，请检查：
1. 日志文件中的错误信息
2. MySQL错误日志
3. 网络连接状态
4. 系统资源使用情况
