# ⭐ 一键启动全量数据获取系统 - 使用指南

## 🎯 最简单的使用方法

### 🚀 一键启动（推荐）
```
双击运行：🚀 快速启动.bat
```

这个主启动器包含了所有功能，是最简单的使用方式！

## 📋 功能菜单说明

当您运行 `🚀 快速启动.bat` 后，会看到以下菜单：

```
【数据获取】
  1. 一键启动数据获取（股票、指数、财务、分红数据）
  2. 系统检查（检查环境和数据库状态）

【数据导出】  
  3. 数据导出工具（导出数据到K盘）

【查看文档】
  4. 打开文档说明目录
```

## 🎯 常用操作指南

### 📊 数据导出（最常用）
**目的**: 将数据库中的数据导出为CSV文件进行分析

**操作步骤**:
1. 双击 `🚀 快速启动.bat`
2. 输入 `3` 选择数据导出
3. 选择 `1` 预览格式，或选择 `2` 批量导出
4. 数据将导出到 `K:\数据库存储地`

**导出特点**:
- ✅ CSV格式，Excel可直接打开
- ✅ 按日期从旧到新排序
- ✅ 大表自动分块处理
- ✅ 包含所有历史数据

### 🔍 系统检查
**目的**: 检查系统环境和数据状态

**操作步骤**:
1. 双击 `🚀 快速启动.bat`
2. 输入 `2` 选择系统检查
3. 查看检查结果

**检查内容**:
- Python环境和依赖包
- 数据库连接状态
- 脚本文件完整性
- Tushare API连接

### 🔄 数据更新
**目的**: 获取最新的股票、财务等数据

**操作步骤**:
1. 双击 `🚀 快速启动.bat`
2. 输入 `1` 选择数据获取
3. 等待执行完成（可能需要几小时）

**注意**: 系统已成功获取了全量数据，通常不需要重新运行

## 📂 目录结构一览

```
📂 项目根目录/
├── 🚀 快速启动.bat          ← 主启动器（推荐使用）
├── 📁 项目结构说明.md       ← 详细结构说明
├── ⭐ 使用指南.md           ← 本文档
│
├── 📂 01-主要工具/          ← 数据获取工具
├── 📂 02-数据导出工具/      ← 数据导出工具  
├── 📂 03-原始数据脚本/      ← 单独脚本
├── 📂 04-文档说明/          ← 所有文档
└── 📂 05-日志记录/          ← 执行日志
```

## 📊 数据概览

### 已获取数据统计
| 数据类型 | 数量 | 时间跨度 | 状态 |
|----------|------|----------|------|
| 股票日线 | 5,423只股票 | 2000-2025 | ✅ 完成 |
| 指数数据 | 8个主要指数 | 1992-2025 | ✅ 完成 |
| 财务数据 | 全市场 | 历史全量 | ✅ 完成 |
| 分红数据 | 全市场 | 历史全量 | ✅ 完成 |

### 数据库分布
- **Daily Line**: 5,423个表（每只股票一个表，约6,000条记录）
- **index**: 8个表（主要指数历史数据）
- **qtdb**: 21,665个表（财务和分红数据）
- **ind**: 8个表（Tushare指数数据）

## 🎯 使用场景

### 📈 数据分析师
**推荐操作**: 数据导出 → 分析
1. 运行数据导出工具
2. 获取CSV文件
3. 用Excel/Python/R进行分析

### 💼 投资研究
**推荐操作**: 定期数据更新 → 导出分析
1. 定期运行数据获取更新最新数据
2. 导出特定股票或时间段的数据
3. 进行投资决策分析

### 🔬 学术研究
**推荐操作**: 一次性导出 → 深度分析
1. 批量导出所有历史数据
2. 构建研究数据集
3. 进行学术研究

## ⚡ 快速问题解决

### ❓ 数据导出失败
**解决方案**:
1. 运行系统检查（选项2）
2. 确认MySQL服务已启动
3. 检查K盘空间是否充足

### ❓ 脚本运行错误
**解决方案**:
1. 查看 `05-日志记录/` 目录下的日志文件
2. 运行系统检查确认环境
3. 查看 `04-文档说明/` 目录下的相关文档

### ❓ 数据格式问题
**解决方案**:
1. 先运行快速预览导出查看格式
2. 确认CSV文件编码为UTF-8
3. 用Excel打开时选择正确的编码

## 🎊 项目特色

### ✅ 简单易用
- 一键启动，无需复杂配置
- 图形化菜单，操作直观
- 详细文档，新手友好

### ✅ 功能完整
- 涵盖股票、指数、财务、分红等全部数据
- 支持历史全量和增量更新
- 提供多种导出格式和排序方式

### ✅ 稳定可靠
- 已成功运行并获取全量数据
- 完善的错误处理和重试机制
- 详细的执行日志记录

## 📞 获取帮助

### 📚 查看文档
运行主启动器，选择选项 `4` 打开文档目录，包含：
- 基础使用说明
- 导出功能详解
- 排序功能说明
- 项目完成总结

### 🔍 问题排查
1. 运行系统检查功能
2. 查看执行日志文件
3. 参考文档说明

---

**使用建议**: 从数据导出开始，这是最常用的功能！  
**技术支持**: 查看 `04-文档说明/` 目录获取详细帮助  
**项目状态**: ✅ 完成并可用
