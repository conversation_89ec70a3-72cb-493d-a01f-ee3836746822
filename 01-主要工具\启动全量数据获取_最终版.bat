@echo off
chcp 65001 >nul
title 一键启动全量数据获取系统 - 最终版

echo ================================================================================
echo                           一键启动全量数据获取系统
echo ================================================================================
echo.
echo 本脚本将自动执行以下操作：
echo 1. 检查Python环境和依赖包
echo 2. 测试数据库连接
echo 3. 创建必要的数据库
echo 4. 执行所有数据获取脚本：
echo    - 股票日线数据获取
echo    - 指数数据获取
echo    - Tushare指数数据获取
echo    - 财务数据获取
echo    - 分红数据获取
echo.
echo 预计总执行时间：6-8小时
echo.
echo 注意：请确保MySQL服务已启动
echo.
pause

echo.
echo 开始执行...
echo.

py "一键启动全量数据获取_最终版.py"

echo.
echo ================================================================================
echo 执行完成！请查看日志文件了解详细结果。
echo ================================================================================
echo.
pause
chcp 65001 >nul
title 一键启动全量数据获取系统 - 最终版

echo ================================================================================
echo                           一键启动全量数据获取系统
echo ================================================================================
echo.
echo 本脚本将自动执行以下操作：
echo 1. 检查Python环境和依赖包
echo 2. 测试数据库连接
echo 3. 创建必要的数据库
echo 4. 执行所有数据获取脚本：
echo    - 股票日线数据获取
echo    - 指数数据获取  
echo    - Tushare指数数据获取
echo    - 财务数据获取
echo    - 分红数据获取
echo.
echo 预计总执行时间：6-8小时
echo.
echo 注意：请确保MySQL服务已启动
echo.
pause

echo.
echo 开始执行...
echo.

py "一键启动全量数据获取_最终版.py"

echo.
echo ================================================================================
echo 执行完成！请查看日志文件了解详细结果。
echo ================================================================================
echo.
pause
