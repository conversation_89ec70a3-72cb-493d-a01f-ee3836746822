#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
一键启动全量数据获取脚本 - 最终版
整合股票日线数据、指数数据、财务数据和分红数据的全量获取功能
"""

import os
import sys
import subprocess
import time
import logging
from datetime import datetime
from pathlib import Path

print("="*80)
print("一键启动全量数据获取系统")
print("="*80)

# 配置Python路径
PYTHON_PATH = "py"

def setup_logging():
    """设置日志配置"""
    try:
        log_dir = Path("../05-日志记录/logs")
        log_dir.mkdir(parents=True, exist_ok=True)

        log_filename = f"全量数据获取_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
        log_path = log_dir / log_filename

        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(str(log_path), encoding='utf-8'),
                logging.StreamHandler(sys.stdout)
            ]
        )

        logger = logging.getLogger(__name__)
        print(f"日志文件: {log_path}")
        return logger
    except Exception as e:
        print(f"日志设置失败: {e}")
        logging.basicConfig(level=logging.INFO)
        return logging.getLogger(__name__)

logger = setup_logging()

def check_dependencies():
    """检查依赖包"""
    logger.info("检查依赖包...")
    packages = ["tushare", "pandas", "sqlalchemy", "pymysql", "numpy"]

    missing = []
    for package in packages:
        try:
            __import__(package)
            logger.info(f"✓ {package}")
        except ImportError:
            logger.error(f"✗ {package}")
            missing.append(package)

    if missing:
        logger.error(f"缺少依赖包: {missing}")
        logger.info("请运行: pip install " + " ".join(missing))
        return False

    logger.info("所有依赖包检查完成")
    return True

def test_database():
    """测试数据库连接"""
    logger.info("测试数据库连接...")
    try:
        import pymysql
        connection = pymysql.connect(
            host="localhost",
            port=3306,
            user="root",
            password="12345678",
            charset="utf8mb4",
            connect_timeout=10
        )

        cursor = connection.cursor()
        cursor.execute("SELECT VERSION()")
        version = cursor.fetchone()
        connection.close()

        logger.info(f"✓ MySQL连接成功，版本: {version[0]}")
        return True

    except Exception as e:
        logger.error(f"✗ MySQL连接失败: {e}")
        logger.warning("数据库连接失败，将跳过需要数据库的脚本")
        return False

def create_databases():
    """创建数据库"""
    logger.info("创建数据库...")
    databases = ["Daily Line", "index", "qtdb", "ind"]

    try:
        import pymysql
        connection = pymysql.connect(
            host="localhost",
            port=3306,
            user="root",
            password="12345678",
            charset="utf8mb4"
        )

        cursor = connection.cursor()
        for db_name in databases:
            try:
                cursor.execute(f"CREATE DATABASE IF NOT EXISTS `{db_name}` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")
                logger.info(f"✓ 数据库 '{db_name}' 创建/验证成功")
            except Exception as e:
                logger.error(f"✗ 数据库 '{db_name}' 创建失败: {e}")

        connection.commit()
        connection.close()
        return True

    except Exception as e:
        logger.error(f"数据库创建失败: {e}")
        return False

def run_script(script_name, description, timeout=3600):
    """运行单个脚本"""
    logger.info(f"开始运行: {description}")

    # 尝试多个可能的路径
    possible_paths = [
        Path("../03-原始数据脚本") / script_name,  # 分类后的路径
        Path("..") / script_name,  # 上级目录
        Path(script_name),  # 当前目录
        Path("../") / script_name  # 备用路径
    ]

    script_path = None
    for path in possible_paths:
        if path.exists():
            script_path = path
            logger.info(f"找到脚本文件: {script_path}")
            break

    if not script_path:
        logger.error(f"脚本文件不存在，已尝试路径:")
        for path in possible_paths:
            logger.error(f"  - {path}")
        return False

    try:
        start_time = time.time()

        result = subprocess.run(
            [PYTHON_PATH, str(script_path)],
            capture_output=True,
            text=True,
            timeout=timeout,
            encoding='utf-8',
            errors='ignore'
        )

        duration = time.time() - start_time

        if result.returncode == 0:
            logger.info(f"✓ {description} 完成 (耗时: {duration:.1f}秒)")
            if result.stdout:
                logger.info(f"输出: {result.stdout[-300:]}")
            return True
        else:
            logger.error(f"✗ {description} 失败")
            if result.stderr:
                logger.error(f"错误: {result.stderr}")
            return False

    except subprocess.TimeoutExpired:
        logger.error(f"✗ {description} 执行超时")
        return False
    except Exception as e:
        logger.error(f"✗ {description} 执行异常: {e}")
        return False

def run_all_scripts(database_available):
    """运行所有脚本"""
    scripts = [
        ("全量股票日线123.py", "股票日线数据获取", 7200, True),
        ("指数全量.py", "指数数据获取", 1800, True),
        ("tushare_index_data.py", "Tushare指数数据获取", 1800, True),
        ("财务数据分开表格全量更新.py", "财务数据获取", 10800, True),
        ("分红数据分开表格全量更新.py", "分红数据获取", 3600, True)
    ]

    if not database_available:
        logger.warning("数据库不可用，跳过所有脚本")
        return 0, 0

    success_count = 0
    total_count = len(scripts)

    for i, (script_name, description, timeout, requires_db) in enumerate(scripts, 1):
        logger.info(f"\n{'='*60}")
        logger.info(f"执行进度: {i}/{total_count}")
        logger.info(f"{'='*60}")

        if run_script(script_name, description, timeout):
            success_count += 1

        if i < total_count:
            logger.info("等待5秒后执行下一个脚本...")
            time.sleep(5)

    return success_count, total_count

def main():
    """主函数"""
    start_time = datetime.now()

    try:
        # 1. 检查依赖
        logger.info("步骤 1: 检查依赖包")
        if not check_dependencies():
            logger.error("依赖检查失败")
            return False

        # 2. 测试数据库
        logger.info("\n步骤 2: 测试数据库连接")
        database_available = test_database()

        if database_available:
            # 3. 创建数据库
            logger.info("\n步骤 3: 创建数据库")
            create_databases()

        # 4. 运行脚本
        logger.info("\n步骤 4: 执行数据获取脚本")
        success_count, total_count = run_all_scripts(database_available)

        # 5. 生成报告
        end_time = datetime.now()
        duration = end_time - start_time

        logger.info(f"\n{'='*80}")
        logger.info("执行报告")
        logger.info(f"{'='*80}")
        logger.info(f"总执行时间: {duration}")
        logger.info(f"成功脚本: {success_count}/{total_count}")

        if success_count == total_count:
            logger.info("🎉 所有脚本执行成功！")
            return True
        else:
            logger.warning(f"⚠️ 部分脚本执行失败")
            return False

    except KeyboardInterrupt:
        logger.info("用户中断执行")
        return False
    except Exception as e:
        logger.error(f"执行过程中发生错误: {e}")
        return False

if __name__ == "__main__":
    success = main()
    input("\n按回车键退出...")
    sys.exit(0 if success else 1)
# -*- coding: utf-8 -*-
"""
一键启动全量数据获取脚本 - 最终版
整合股票日线数据、指数数据、财务数据和分红数据的全量获取功能
"""

import os
import sys
import subprocess
import time
import logging
from datetime import datetime
from pathlib import Path
import json

print("="*80)
print("一键启动全量数据获取系统")
print("="*80)

# 配置Python路径
PYTHON_PATH = "py"

# 配置信息
CONFIG = {
    "TUSHARE_TOKEN": "2876ea85cb005fb5fa17c809a98174f2d5aae8b1f830110a5ead6211",
    "MYSQL_CONFIG": {
        "host": "localhost",
        "port": 3306,
        "user": "root",
        "password": "12345678",
        "charset": "utf8mb4"
    }
}

def setup_logging():
    """设置日志配置"""
    try:
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)
        
        log_filename = f"全量数据获取_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
        log_path = log_dir / log_filename
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(str(log_path), encoding='utf-8'),
                logging.StreamHandler(sys.stdout)
            ]
        )
        
        logger = logging.getLogger(__name__)
        print(f"日志文件: {log_path}")
        return logger
    except Exception as e:
        print(f"日志设置失败: {e}")
        logging.basicConfig(level=logging.INFO)
        return logging.getLogger(__name__)

logger = setup_logging()

def check_dependencies():
    """检查依赖包"""
    logger.info("检查依赖包...")
    packages = ["tushare", "pandas", "sqlalchemy", "pymysql", "numpy"]
    
    missing = []
    for package in packages:
        try:
            __import__(package)
            logger.info(f"✓ {package}")
        except ImportError:
            logger.error(f"✗ {package}")
            missing.append(package)
    
    if missing:
        logger.error(f"缺少依赖包: {missing}")
        logger.info("请运行: pip install " + " ".join(missing))
        return False
    
    logger.info("所有依赖包检查完成")
    return True

def test_database():
    """测试数据库连接"""
    logger.info("测试数据库连接...")
    try:
        import pymysql
        connection = pymysql.connect(
            host=CONFIG["MYSQL_CONFIG"]["host"],
            port=CONFIG["MYSQL_CONFIG"]["port"],
            user=CONFIG["MYSQL_CONFIG"]["user"],
            password=CONFIG["MYSQL_CONFIG"]["password"],
            charset=CONFIG["MYSQL_CONFIG"]["charset"],
            connect_timeout=10
        )
        
        cursor = connection.cursor()
        cursor.execute("SELECT VERSION()")
        version = cursor.fetchone()
        connection.close()
        
        logger.info(f"✓ MySQL连接成功，版本: {version[0]}")
        return True
        
    except Exception as e:
        logger.error(f"✗ MySQL连接失败: {e}")
        logger.warning("数据库连接失败，将跳过需要数据库的脚本")
        return False

def create_databases():
    """创建数据库"""
    logger.info("创建数据库...")
    databases = ["Daily Line", "index", "qtdb", "ind"]
    
    try:
        import pymysql
        connection = pymysql.connect(
            host=CONFIG["MYSQL_CONFIG"]["host"],
            port=CONFIG["MYSQL_CONFIG"]["port"],
            user=CONFIG["MYSQL_CONFIG"]["user"],
            password=CONFIG["MYSQL_CONFIG"]["password"],
            charset=CONFIG["MYSQL_CONFIG"]["charset"]
        )
        
        cursor = connection.cursor()
        for db_name in databases:
            try:
                cursor.execute(f"CREATE DATABASE IF NOT EXISTS `{db_name}` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")
                logger.info(f"✓ 数据库 '{db_name}' 创建/验证成功")
            except Exception as e:
                logger.error(f"✗ 数据库 '{db_name}' 创建失败: {e}")
        
        connection.commit()
        connection.close()
        return True
        
    except Exception as e:
        logger.error(f"数据库创建失败: {e}")
        return False

def run_script(script_name, description, timeout=3600):
    """运行单个脚本"""
    logger.info(f"开始运行: {description}")
    
    if not Path(script_name).exists():
        logger.error(f"脚本文件不存在: {script_name}")
        return False
    
    try:
        start_time = time.time()
        
        result = subprocess.run(
            [PYTHON_PATH, script_name],
            capture_output=True,
            text=True,
            timeout=timeout,
            encoding='utf-8',
            errors='ignore'
        )
        
        duration = time.time() - start_time
        
        if result.returncode == 0:
            logger.info(f"✓ {description} 完成 (耗时: {duration:.1f}秒)")
            if result.stdout:
                logger.info(f"输出: {result.stdout[-300:]}")
            return True
        else:
            logger.error(f"✗ {description} 失败")
            if result.stderr:
                logger.error(f"错误: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        logger.error(f"✗ {description} 执行超时")
        return False
    except Exception as e:
        logger.error(f"✗ {description} 执行异常: {e}")
        return False

def run_all_scripts(database_available):
    """运行所有脚本"""
    scripts = [
        ("全量股票日线123.py", "股票日线数据获取", 7200, True),
        ("指数全量.py", "指数数据获取", 1800, True),
        ("tushare_index_data.py", "Tushare指数数据获取", 1800, True),
        ("财务数据分开表格全量更新.py", "财务数据获取", 10800, True),
        ("分红数据分开表格全量更新.py", "分红数据获取", 3600, True)
    ]
    
    if not database_available:
        logger.warning("数据库不可用，跳过所有脚本")
        return 0, 0
    
    success_count = 0
    total_count = len(scripts)
    
    for i, (script_name, description, timeout, requires_db) in enumerate(scripts, 1):
        logger.info(f"\n{'='*60}")
        logger.info(f"执行进度: {i}/{total_count}")
        logger.info(f"{'='*60}")
        
        if run_script(script_name, description, timeout):
            success_count += 1
        
        if i < total_count:
            logger.info("等待5秒后执行下一个脚本...")
            time.sleep(5)
    
    return success_count, total_count

def main():
    """主函数"""
    start_time = datetime.now()
    
    try:
        # 1. 检查依赖
        logger.info("步骤 1: 检查依赖包")
        if not check_dependencies():
            logger.error("依赖检查失败")
            return False
        
        # 2. 测试数据库
        logger.info("\n步骤 2: 测试数据库连接")
        database_available = test_database()
        
        if database_available:
            # 3. 创建数据库
            logger.info("\n步骤 3: 创建数据库")
            create_databases()
        
        # 4. 运行脚本
        logger.info("\n步骤 4: 执行数据获取脚本")
        success_count, total_count = run_all_scripts(database_available)
        
        # 5. 生成报告
        end_time = datetime.now()
        duration = end_time - start_time
        
        logger.info(f"\n{'='*80}")
        logger.info("执行报告")
        logger.info(f"{'='*80}")
        logger.info(f"总执行时间: {duration}")
        logger.info(f"成功脚本: {success_count}/{total_count}")
        
        if success_count == total_count:
            logger.info("🎉 所有脚本执行成功！")
            return True
        else:
            logger.warning(f"⚠️ 部分脚本执行失败")
            return False
            
    except KeyboardInterrupt:
        logger.info("用户中断执行")
        return False
    except Exception as e:
        logger.error(f"执行过程中发生错误: {e}")
        return False

if __name__ == "__main__":
    success = main()
    input("\n按回车键退出...")
    sys.exit(0 if success else 1)
