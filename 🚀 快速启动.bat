@echo off
chcp 65001 >nul
title 一键启动全量数据获取系统 - 快速启动

echo ================================================================================
echo                    一键启动全量数据获取系统 - 快速启动
echo ================================================================================
echo.
echo 请选择要执行的操作：
echo.
echo 【数据获取】
echo   1. 一键启动数据获取（股票、指数、财务、分红数据）
echo   2. 系统检查（检查环境和数据库状态）
echo.
echo 【数据导出】  
echo   3. 数据导出工具（导出数据到K盘）
echo.
echo 【查看文档】
echo   4. 打开文档说明目录
echo.
echo   0. 退出
echo.
echo ================================================================================

set /p choice="请输入选择 (0-4): "

if "%choice%"=="1" (
    echo.
    echo 启动数据获取...
    cd /d "01-主要工具"
    call "启动全量数据获取_最终版.bat"
    cd /d ..
) else if "%choice%"=="2" (
    echo.
    echo 运行系统检查...
    cd /d "01-主要工具"
    py "系统检查.py"
    cd /d ..
) else if "%choice%"=="3" (
    echo.
    echo 启动数据导出工具...
    cd /d "02-数据导出工具"
    call "启动数据库导出.bat"
    cd /d ..
) else if "%choice%"=="4" (
    echo.
    echo 打开文档目录...
    start "" "04-文档说明"
) else if "%choice%"=="0" (
    echo 退出
    exit /b 0
) else (
    echo 无效选择，请重试
    pause
    goto :eof
)

echo.
echo 操作完成！
pause
