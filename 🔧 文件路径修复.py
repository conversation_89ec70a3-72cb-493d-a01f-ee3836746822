#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
文件路径修复脚本
自动查找原始数据脚本的实际位置并修复路径
"""

import os
import sys
from pathlib import Path
import shutil

def find_script_files():
    """查找原始数据脚本文件"""
    print("="*60)
    print("查找原始数据脚本文件")
    print("="*60)
    
    target_scripts = [
        "全量股票日线123.py",
        "指数全量.py", 
        "tushare_index_data.py",
        "财务数据分开表格全量更新.py",
        "分红数据分开表格全量更新.py"
    ]
    
    current_dir = Path.cwd()
    found_files = {}
    
    print(f"当前目录: {current_dir}")
    print(f"查找目标文件...")
    
    # 在当前目录及其子目录中查找
    for script in target_scripts:
        print(f"\n查找: {script}")
        
        # 可能的位置
        search_paths = [
            current_dir / script,  # 根目录
            current_dir / "03-原始数据脚本" / script,  # 分类目录
            current_dir.parent / script,  # 上级目录
        ]
        
        # 递归搜索
        for root, dirs, files in os.walk(current_dir):
            if script in files:
                found_path = Path(root) / script
                search_paths.append(found_path)
        
        # 检查每个可能的路径
        for path in search_paths:
            if path.exists():
                print(f"  ✓ 找到: {path}")
                found_files[script] = path
                break
        else:
            print(f"  ✗ 未找到: {script}")
    
    return found_files

def create_script_directory():
    """创建03-原始数据脚本目录"""
    script_dir = Path.cwd() / "03-原始数据脚本"
    script_dir.mkdir(exist_ok=True)
    print(f"✓ 创建目录: {script_dir}")
    return script_dir

def copy_scripts_to_directory(found_files):
    """复制脚本到正确目录"""
    print(f"\n{'='*60}")
    print("复制脚本到正确目录")
    print("="*60)
    
    script_dir = create_script_directory()
    
    copied_count = 0
    for script_name, source_path in found_files.items():
        target_path = script_dir / script_name
        
        try:
            if source_path != target_path:  # 避免自己复制自己
                shutil.copy2(source_path, target_path)
                print(f"✓ 复制: {script_name}")
                print(f"  从: {source_path}")
                print(f"  到: {target_path}")
                copied_count += 1
            else:
                print(f"✓ 已在正确位置: {script_name}")
                copied_count += 1
        except Exception as e:
            print(f"✗ 复制失败: {script_name} - {e}")
    
    return copied_count

def create_alternative_launcher():
    """创建备用启动脚本"""
    print(f"\n{'='*60}")
    print("创建备用启动脚本")
    print("="*60)
    
    launcher_content = '''#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
备用启动脚本 - 自动查找脚本路径
"""

import os
import sys
import subprocess
import time
from pathlib import Path

def find_and_run_script(script_name, description):
    """查找并运行脚本"""
    print(f"\\n运行: {description}")
    
    # 可能的路径
    search_paths = [
        Path("03-原始数据脚本") / script_name,
        Path(script_name),
        Path("..") / script_name,
        Path("../03-原始数据脚本") / script_name
    ]
    
    # 递归搜索
    for root, dirs, files in os.walk("."):
        if script_name in files:
            search_paths.append(Path(root) / script_name)
    
    script_path = None
    for path in search_paths:
        if path.exists():
            script_path = path
            break
    
    if not script_path:
        print(f"✗ 找不到脚本: {script_name}")
        return False
    
    print(f"✓ 找到脚本: {script_path}")
    
    try:
        result = subprocess.run([sys.executable, str(script_path)], 
                              capture_output=True, text=True, timeout=3600)
        
        if result.returncode == 0:
            print(f"✓ {description} 执行成功")
            return True
        else:
            print(f"✗ {description} 执行失败")
            if result.stderr:
                print(f"错误: {result.stderr}")
            return False
    except Exception as e:
        print(f"✗ {description} 执行异常: {e}")
        return False

def main():
    """主函数"""
    print("备用数据获取启动器")
    print("="*50)
    
    scripts = [
        ("全量股票日线123.py", "股票日线数据获取"),
        ("指数全量.py", "指数数据获取"),
        ("tushare_index_data.py", "Tushare指数数据获取"),
        ("财务数据分开表格全量更新.py", "财务数据获取"),
        ("分红数据分开表格全量更新.py", "分红数据获取")
    ]
    
    success_count = 0
    for script_name, description in scripts:
        if find_and_run_script(script_name, description):
            success_count += 1
        time.sleep(2)
    
    print(f"\\n执行完成: {success_count}/{len(scripts)} 成功")

if __name__ == "__main__":
    main()
    input("\\n按回车键退出...")
'''
    
    launcher_path = Path.cwd() / "备用启动器.py"
    with open(launcher_path, 'w', encoding='utf-8') as f:
        f.write(launcher_content)
    
    print(f"✓ 创建备用启动器: {launcher_path}")
    return launcher_path

def main():
    """主函数"""
    print("文件路径修复工具")
    print("自动查找并修复原始数据脚本的路径问题")
    
    # 1. 查找脚本文件
    found_files = find_script_files()
    
    if not found_files:
        print(f"\\n✗ 没有找到任何原始数据脚本文件")
        print(f"请确认这些文件存在于项目目录中")
        return False
    
    print(f"\\n找到 {len(found_files)} 个脚本文件")
    
    # 2. 复制到正确目录
    copied_count = copy_scripts_to_directory(found_files)
    
    # 3. 创建备用启动器
    launcher_path = create_alternative_launcher()
    
    print(f"\\n{'='*60}")
    print("修复完成")
    print("="*60)
    print(f"✓ 复制了 {copied_count} 个脚本文件")
    print(f"✓ 创建了备用启动器")
    
    print(f"\\n现在您可以:")
    print(f"1. 重新运行主启动脚本")
    print(f"2. 或使用备用启动器: py \\"备用启动器.py\\"")
    
    return True

if __name__ == "__main__":
    main()
    input("\\n按回车键退出...")
