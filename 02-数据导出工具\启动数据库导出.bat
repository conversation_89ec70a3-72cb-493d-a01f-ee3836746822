@echo off
chcp 65001 >nul
title 数据库导出工具

echo ================================================================================
echo                              数据库导出工具
echo ================================================================================
echo.
echo 本工具将导出以下数据库的所有数据到 K:\数据库存储地：
echo.
echo 1. Daily Line    - 股票日线数据
echo 2. index         - 指数数据
echo 3. qtdb          - 财务和分红数据
echo 4. ind           - Tushare指数数据
echo.
echo 导出格式：CSV文件（大表会分块导出）
echo.
echo 注意事项：
echo - 确保有足够的磁盘空间（建议至少10GB）
echo - 导出过程可能需要1-3小时
echo - 请不要中断程序执行
echo - 确保MySQL服务正在运行
echo.

echo 请选择操作：
echo 1. 快速预览（导出样本数据查看格式）
echo 2. 批量导出所有数据
echo 3. 退出
echo.

set /p choice="请输入选择 (1-3): "

if "%choice%"=="1" (
    echo.
    echo 开始快速预览...
    py "快速预览导出.py"
) else if "%choice%"=="2" (
    echo.
    echo 开始批量导出...
    py "批量导出所有数据.py"
) else if "%choice%"=="3" (
    echo 退出
    exit /b 0
) else (
    echo 无效选择
    pause
    goto :eof
)

echo.
echo ================================================================================
echo 操作完成！
echo ================================================================================
pause
chcp 65001 >nul
title 数据库导出工具

echo ================================================================================
echo                              数据库导出工具
echo ================================================================================
echo.
echo 本工具将导出以下数据库的所有数据到 K:\数据库存储地：
echo.
echo 1. Daily Line    - 股票日线数据
echo 2. index         - 指数数据  
echo 3. qtdb          - 财务和分红数据
echo 4. ind           - Tushare指数数据
echo.
echo 导出格式：CSV文件（大表会分块导出）
echo.
echo 注意事项：
echo - 确保有足够的磁盘空间（建议至少10GB）
echo - 导出过程可能需要1-3小时
echo - 请不要中断程序执行
echo - 确保MySQL服务正在运行
echo.

echo 请选择操作：
echo 1. 快速预览（导出样本数据查看格式）
echo 2. 批量导出所有数据
echo 3. 退出
echo.

set /p choice="请输入选择 (1-3): "

if "%choice%"=="1" (
    echo.
    echo 开始快速预览...
    py "快速预览导出.py"
) else if "%choice%"=="2" (
    echo.
    echo 开始批量导出...
    py "批量导出所有数据.py"
) else if "%choice%"=="3" (
    echo 退出
    exit /b 0
) else (
    echo 无效选择
    pause
    goto :eof
)

echo.
echo ================================================================================
echo 操作完成！
echo ================================================================================
pause
