#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
快速预览导出工具
先导出一个表查看格式
"""

import pandas as pd
import pymysql
from pathlib import Path
import sys

# 配置
EXPORT_DIR = Path(r"K:\数据库存储地")
MYSQL_CONFIG = {
    "host": "localhost",
    "port": 3306,
    "user": "root",
    "password": "12345678",
    "charset": "utf8mb4"
}

def get_connection(database=None):
    """获取数据库连接"""
    try:
        config = MYSQL_CONFIG.copy()
        if database:
            config['database'] = database
        return pymysql.connect(**config)
    except Exception as e:
        print(f"数据库连接失败: {e}")
        return None

def get_date_column_for_preview(database):
    """获取预览用的日期列名"""
    date_columns = {
        "Daily Line": "trade_date",
        "index": "candle_end_time",
        "ind": "transaction_date",
        "qtdb": "end_date"
    }
    return date_columns.get(database, None)

def preview_database_structure():
    """预览数据库结构"""
    print("数据库结构预览:")
    print("="*60)

    databases = ["Daily Line", "index", "qtdb", "ind"]

    for db_name in databases:
        print(f"\n数据库: {db_name}")
        print("-" * 40)

        try:
            conn = get_connection(db_name)
            if not conn:
                print("  连接失败")
                continue

            cursor = conn.cursor()
            cursor.execute("SHOW TABLES")
            tables = cursor.fetchall()

            print(f"  表数量: {len(tables)}")

            # 显示前5个表的信息
            for i, (table_name,) in enumerate(tables[:5]):
                cursor.execute(f"SELECT COUNT(*) FROM `{table_name}`")
                count = cursor.fetchone()[0]
                print(f"  {i+1}. {table_name}: {count:,} 行")

            if len(tables) > 5:
                print(f"  ... 还有 {len(tables)-5} 个表")

            conn.close()

        except Exception as e:
            print(f"  错误: {e}")

def export_sample_table():
    """导出一个样本表"""
    print("\n开始导出样本表...")

    # 创建导出目录
    EXPORT_DIR.mkdir(parents=True, exist_ok=True)

    # 尝试找一个有数据的表
    databases = ["Daily Line", "index", "qtdb", "ind"]

    for db_name in databases:
        try:
            conn = get_connection(db_name)
            if not conn:
                continue

            cursor = conn.cursor()
            cursor.execute("SHOW TABLES")
            tables = cursor.fetchall()

            for table_name, in tables[:3]:  # 检查前3个表
                cursor.execute(f"SELECT COUNT(*) FROM `{table_name}`")
                count = cursor.fetchone()[0]

                if count > 0:
                    print(f"找到有数据的表: {db_name}.{table_name} ({count:,} 行)")

                    # 获取日期列用于排序
                    date_column = get_date_column_for_preview(db_name)
                    order_clause = ""
                    if date_column:
                        # 检查表中是否存在该日期列
                        cursor.execute(f"DESCRIBE `{table_name}`")
                        columns = [col[0] for col in cursor.fetchall()]
                        if date_column in columns:
                            order_clause = f" ORDER BY `{date_column}` ASC"
                            print(f"  按 {date_column} 从旧到新排序")

                    # 导出样本数据（从旧到新）
                    query = f"SELECT * FROM `{table_name}`{order_clause} LIMIT 100"
                    df = pd.read_sql(query, conn)

                    # 创建数据库目录
                    db_dir = EXPORT_DIR / db_name
                    db_dir.mkdir(exist_ok=True)

                    # 保存为CSV
                    csv_file = db_dir / f"{table_name}_样本.csv"
                    df.to_csv(csv_file, index=False, encoding='utf-8-sig')

                    # 保存为Excel
                    excel_file = db_dir / f"{table_name}_样本.xlsx"
                    df.to_excel(excel_file, index=False)

                    print(f"样本已导出:")
                    print(f"  CSV: {csv_file}")
                    print(f"  Excel: {excel_file}")
                    print(f"  样本行数: {len(df)}")
                    print(f"  列数: {len(df.columns)}")
                    print(f"  列名: {', '.join(df.columns[:10])}{'...' if len(df.columns) > 10 else ''}")

                    # 显示前几行数据
                    print(f"\n前5行数据预览:")
                    print(df.head().to_string())

                    conn.close()
                    return True

            conn.close()

        except Exception as e:
            print(f"处理数据库 {db_name} 时出错: {e}")

    print("没有找到有数据的表")
    return False

def main():
    """主函数"""
    print("="*60)
    print("快速预览导出工具")
    print("="*60)

    try:
        # 1. 预览数据库结构
        preview_database_structure()

        # 2. 导出样本表
        print("\n" + "="*60)
        success = export_sample_table()

        if success:
            print(f"\n样本导出完成！")
            print(f"导出目录: {EXPORT_DIR}")
            print(f"\n请检查导出的文件格式是否符合要求。")
            print(f"如果格式正确，可以运行完整导出工具。")
        else:
            print("\n样本导出失败，请检查数据库连接和数据。")

    except Exception as e:
        print(f"执行过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
    input("\n按回车键退出...")
# -*- coding: utf-8 -*-
"""
快速预览导出工具
先导出一个表查看格式
"""

import pandas as pd
import pymysql
from pathlib import Path
import sys

# 配置
EXPORT_DIR = Path(r"K:\数据库存储地")
MYSQL_CONFIG = {
    "host": "localhost",
    "port": 3306,
    "user": "root",
    "password": "12345678",
    "charset": "utf8mb4"
}

def get_connection(database=None):
    """获取数据库连接"""
    try:
        config = MYSQL_CONFIG.copy()
        if database:
            config['database'] = database
        return pymysql.connect(**config)
    except Exception as e:
        print(f"数据库连接失败: {e}")
        return None

def preview_database_structure():
    """预览数据库结构"""
    print("数据库结构预览:")
    print("="*60)
    
    databases = ["Daily Line", "index", "qtdb", "ind"]
    
    for db_name in databases:
        print(f"\n数据库: {db_name}")
        print("-" * 40)
        
        try:
            conn = get_connection(db_name)
            if not conn:
                print("  连接失败")
                continue
            
            cursor = conn.cursor()
            cursor.execute("SHOW TABLES")
            tables = cursor.fetchall()
            
            print(f"  表数量: {len(tables)}")
            
            # 显示前5个表的信息
            for i, (table_name,) in enumerate(tables[:5]):
                cursor.execute(f"SELECT COUNT(*) FROM `{table_name}`")
                count = cursor.fetchone()[0]
                print(f"  {i+1}. {table_name}: {count:,} 行")
            
            if len(tables) > 5:
                print(f"  ... 还有 {len(tables)-5} 个表")
            
            conn.close()
            
        except Exception as e:
            print(f"  错误: {e}")

def get_date_column_for_preview(database):
    """获取预览用的日期列名"""
    date_columns = {
        "Daily Line": "trade_date",
        "index": "candle_end_time",
        "ind": "transaction_date",
        "qtdb": "end_date"
    }
    return date_columns.get(database, None)

def export_sample_table():
    """导出一个样本表"""
    print("\n开始导出样本表...")

    # 创建导出目录
    EXPORT_DIR.mkdir(parents=True, exist_ok=True)

    # 尝试找一个有数据的表
    databases = ["Daily Line", "index", "qtdb", "ind"]

    for db_name in databases:
        try:
            conn = get_connection(db_name)
            if not conn:
                continue

            cursor = conn.cursor()
            cursor.execute("SHOW TABLES")
            tables = cursor.fetchall()

            for table_name, in tables[:3]:  # 检查前3个表
                cursor.execute(f"SELECT COUNT(*) FROM `{table_name}`")
                count = cursor.fetchone()[0]

                if count > 0:
                    print(f"找到有数据的表: {db_name}.{table_name} ({count:,} 行)")

                    # 获取日期列用于排序
                    date_column = get_date_column_for_preview(db_name)
                    order_clause = ""
                    if date_column:
                        # 检查表中是否存在该日期列
                        cursor.execute(f"DESCRIBE `{table_name}`")
                        columns = [col[0] for col in cursor.fetchall()]
                        if date_column in columns:
                            order_clause = f" ORDER BY `{date_column}` ASC"
                            print(f"  按 {date_column} 从旧到新排序")

                    # 导出样本数据（从旧到新）
                    query = f"SELECT * FROM `{table_name}`{order_clause} LIMIT 100"
                    df = pd.read_sql(query, conn)
                    
                    # 创建数据库目录
                    db_dir = EXPORT_DIR / db_name
                    db_dir.mkdir(exist_ok=True)
                    
                    # 保存为CSV
                    csv_file = db_dir / f"{table_name}_样本.csv"
                    df.to_csv(csv_file, index=False, encoding='utf-8-sig')
                    
                    # 保存为Excel
                    excel_file = db_dir / f"{table_name}_样本.xlsx"
                    df.to_excel(excel_file, index=False)
                    
                    print(f"样本已导出:")
                    print(f"  CSV: {csv_file}")
                    print(f"  Excel: {excel_file}")
                    print(f"  样本行数: {len(df)}")
                    print(f"  列数: {len(df.columns)}")
                    print(f"  列名: {', '.join(df.columns[:10])}{'...' if len(df.columns) > 10 else ''}")
                    
                    # 显示前几行数据
                    print(f"\n前5行数据预览:")
                    print(df.head().to_string())
                    
                    conn.close()
                    return True
            
            conn.close()
            
        except Exception as e:
            print(f"处理数据库 {db_name} 时出错: {e}")
    
    print("没有找到有数据的表")
    return False

def main():
    """主函数"""
    print("="*60)
    print("快速预览导出工具")
    print("="*60)
    
    try:
        # 1. 预览数据库结构
        preview_database_structure()
        
        # 2. 导出样本表
        print("\n" + "="*60)
        success = export_sample_table()
        
        if success:
            print(f"\n样本导出完成！")
            print(f"导出目录: {EXPORT_DIR}")
            print(f"\n请检查导出的文件格式是否符合要求。")
            print(f"如果格式正确，可以运行完整导出工具。")
        else:
            print("\n样本导出失败，请检查数据库连接和数据。")
    
    except Exception as e:
        print(f"执行过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
    input("\n按回车键退出...")
