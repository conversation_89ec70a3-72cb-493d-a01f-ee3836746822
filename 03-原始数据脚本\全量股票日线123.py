import tushare as ts
import pandas as pd
from sqlalchemy import create_engine, text
import time
import random
import logging
import json
from typing import List, Dict, Any

# Configuration
TUSHARE_TOKEN = "2876ea85cb005fb5fa17c809a98174f2d5aae8b1f830110a5ead6211"
DATABASE_NAME = "Daily Line"
DATABASE_URL = f"mysql+pymysql://root:12345678@localhost:3306/{DATABASE_NAME}?charset=utf8mb4"
BASE_DATABASE_URL = "mysql+pymysql://root:12345678@localhost:3306?charset=utf8mb4"  # Without database name

# Rate limiting configuration
MAX_REQUESTS_PER_MINUTE = 950  # Conservative limit with buffer
BATCH_SIZE = 10  # Process stocks in batches
RETRY_ATTEMPTS = 3
BASE_DELAY = 1  # Base delay for exponential backoff

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('stock_data_fetch.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Initialize Tushare
ts.set_token(TUSHARE_TOKEN)
pro = ts.pro_api()

def create_database_if_not_exists():
    """Create the database if it doesn't exist"""
    try:
        # Connect to MySQL server without specifying database
        base_engine = create_engine(BASE_DATABASE_URL)

        with base_engine.connect() as connection:
            # Check if database exists
            result = connection.execute(
                text(f"SELECT SCHEMA_NAME FROM INFORMATION_SCHEMA.SCHEMATA WHERE SCHEMA_NAME = '{DATABASE_NAME}'")
            )

            if not result.fetchone():
                # Database doesn't exist, create it
                connection.execute(text(f"CREATE DATABASE `{DATABASE_NAME}` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci"))
                connection.commit()
                logger.info(f"Created database: {DATABASE_NAME}")
            else:
                logger.info(f"Database '{DATABASE_NAME}' already exists")

        base_engine.dispose()

    except Exception as e:
        logger.error(f"Error creating database: {e}")
        raise

# Create database if needed and then create engine
create_database_if_not_exists()
engine = create_engine(DATABASE_URL)

def create_stock_summary_table():
    """Create a summary table to track all stock tables"""
    try:
        summary_sql = """
        CREATE TABLE IF NOT EXISTS stock_summary (
            id INT AUTO_INCREMENT PRIMARY KEY,
            ts_code VARCHAR(20) NOT NULL,
            symbol VARCHAR(20) NOT NULL,
            name VARCHAR(100) NOT NULL,
            table_name VARCHAR(50) NOT NULL,
            last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            record_count INT DEFAULT 0,
            start_date DATE,
            end_date DATE,
            UNIQUE KEY unique_stock (ts_code)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """

        with engine.connect() as connection:
            connection.execute(text(summary_sql))
            connection.commit()
            logger.info("Stock summary table created/verified")

    except Exception as e:
        logger.error(f"Error creating summary table: {e}")
        raise

def update_stock_summary(ts_code: str, symbol: str, name: str, table_name: str, record_count: int, start_date: str = None, end_date: str = None):
    """Update the stock summary table with processing information"""
    try:
        update_sql = """
        INSERT INTO stock_summary (ts_code, symbol, name, table_name, record_count, start_date, end_date)
        VALUES (:ts_code, :symbol, :name, :table_name, :record_count, :start_date, :end_date)
        ON DUPLICATE KEY UPDATE
            name = VALUES(name),
            table_name = VALUES(table_name),
            record_count = record_count + VALUES(record_count),
            last_updated = CURRENT_TIMESTAMP,
            end_date = COALESCE(VALUES(end_date), end_date)
        """

        with engine.connect() as connection:
            connection.execute(text(update_sql), {
                'ts_code': ts_code,
                'symbol': symbol,
                'name': name,
                'table_name': table_name,
                'record_count': record_count,
                'start_date': start_date,
                'end_date': end_date
            })
            connection.commit()

    except Exception as e:
        logger.error(f"Error updating stock summary for {ts_code}: {e}")

# Create summary table
create_stock_summary_table()

class RateLimiter:
    """Enhanced rate limiter with exponential backoff"""

    def __init__(self, max_requests_per_minute: int = MAX_REQUESTS_PER_MINUTE):
        self.max_requests = max_requests_per_minute
        self.request_count = 0
        self.start_time = time.time()
        self.last_request_time = 0

    def wait_if_needed(self):
        """Wait if rate limit is approaching"""
        current_time = time.time()

        # Reset counter if a minute has passed
        if current_time - self.start_time >= 60:
            self.request_count = 0
            self.start_time = current_time

        # If approaching limit, wait for the minute to reset
        if self.request_count >= self.max_requests:
            elapsed = current_time - self.start_time
            if elapsed < 60:
                wait_time = 60 - elapsed + random.uniform(1, 3)  # Add jitter
                logger.info(f"Rate limit reached, waiting {wait_time:.1f} seconds...")
                time.sleep(wait_time)
                self.request_count = 0
                self.start_time = time.time()

        # Add small delay between requests to avoid bursts
        time_since_last = current_time - self.last_request_time
        if time_since_last < 0.1:  # Minimum 100ms between requests
            time.sleep(0.1 - time_since_last)

        self.last_request_time = time.time()

    def increment(self):
        """Increment request counter"""
        self.request_count += 1

def api_call_with_retry(func, *args, max_retries: int = RETRY_ATTEMPTS, **kwargs):
    """Make API call with exponential backoff retry"""
    for attempt in range(max_retries):
        try:
            rate_limiter.wait_if_needed()
            result = func(*args, **kwargs)
            rate_limiter.increment()
            return result
        except Exception as e:
            error_msg = str(e).lower()

            # Check if it's a rate limit error
            if "1000 times per minute" in error_msg or "rate limit" in error_msg:
                if attempt < max_retries - 1:
                    # Exponential backoff with jitter
                    delay = (BASE_DELAY * (2 ** attempt)) + random.uniform(0, 1)
                    logger.warning(f"Rate limit hit, retrying in {delay:.1f}s (attempt {attempt + 1}/{max_retries})")
                    time.sleep(delay)
                    continue
                else:
                    logger.error(f"Rate limit exceeded after {max_retries} attempts")
                    raise
            else:
                # For other errors, retry with shorter delay
                if attempt < max_retries - 1:
                    delay = 0.5 * (attempt + 1)
                    logger.warning(f"API error: {e}, retrying in {delay:.1f}s (attempt {attempt + 1}/{max_retries})")
                    time.sleep(delay)
                    continue
                else:
                    logger.error(f"API call failed after {max_retries} attempts: {e}")
                    raise

    return None

# Global rate limiter instance
rate_limiter = RateLimiter()

def fetch_all_stocks_daily_data(start_date=None, end_date=None, reweight_factor=1.0):
    """
    Fetch daily data for all stocks with daily indicators
    Enhanced with robust rate limiting and error handling
    """
    try:
        # Get all stock codes with retry
        logger.info("Fetching stock list...")
        stock_list = api_call_with_retry(
            pro.stock_basic,
            exchange='',
            list_status='L',
            fields='ts_code,symbol,name'
        )

        if stock_list is None or stock_list.empty:
            logger.error("Failed to fetch stock list")
            return None

        total_stocks = len(stock_list)
        logger.info(f"Total stocks to process: {total_stocks}")

        processed_count = 0
        failed_stocks = []

        # Process stocks in batches to better manage memory and progress tracking
        for batch_start in range(0, total_stocks, BATCH_SIZE):
            batch_end = min(batch_start + BATCH_SIZE, total_stocks)
            batch_stocks = stock_list.iloc[batch_start:batch_end]

            logger.info(f"Processing batch {batch_start//BATCH_SIZE + 1}/{(total_stocks-1)//BATCH_SIZE + 1} "
                       f"(stocks {batch_start + 1}-{batch_end})")

            for _, stock in batch_stocks.iterrows():
                ts_code = stock['ts_code']
                symbol = stock['symbol']
                name = stock['name']

                try:
                    # Get daily price data with retry
                    df_daily = api_call_with_retry(
                        pro.daily,
                        ts_code=ts_code,
                        start_date=start_date,
                        end_date=end_date
                    )

                    if df_daily is None or df_daily.empty:
                        logger.warning(f"No daily data for {ts_code} ({name})")
                        continue

                    # Get daily indicators with retry
                    df_basic = api_call_with_retry(
                        pro.daily_basic,
                        ts_code=ts_code,
                        start_date=start_date,
                        end_date=end_date,
                        fields='ts_code,trade_date,close,turnover_rate,turnover_rate_f,volume_ratio,pe,pe_ttm,pb,ps,ps_ttm,dv_ratio,dv_ttm,total_share,float_share,free_share,total_mv,circ_mv'
                    )

                    if df_basic is None:
                        logger.warning(f"No basic data for {ts_code} ({name})")
                        df_basic = pd.DataFrame()  # Create empty DataFrame for merge

                    # Merge data
                    if not df_basic.empty:
                        df = pd.merge(df_daily, df_basic, on=['ts_code', 'trade_date'], how='left', suffixes=('', '_basic'))
                    else:
                        df = df_daily.copy()

                    # Add re-weighting factor
                    df['reweight_factor'] = reweight_factor
                    df['reweighted_close'] = df['close'] * df['reweight_factor']
                    df['reweighted_open'] = df['open'] * df['reweight_factor']
                    df['reweighted_high'] = df['high'] * df['reweight_factor']
                    df['reweighted_low'] = df['low'] * df['reweight_factor']

                    # Create table name using stock symbol
                    table_name = f'stock_daily_{symbol}'

                    # Store to database
                    df.to_sql(
                        name=table_name,
                        con=engine,
                        if_exists='append',
                        index=False
                    )

                    # Update summary table
                    start_date_val = df['trade_date'].min() if not df.empty else start_date
                    end_date_val = df['trade_date'].max() if not df.empty else end_date
                    update_stock_summary(ts_code, symbol, name, table_name, len(df), start_date_val, end_date_val)

                    processed_count += 1
                    logger.info(f"Processed {processed_count}/{total_stocks}: {ts_code} ({name}) - {len(df)} records")

                except Exception as stock_error:
                    logger.error(f"Error processing {ts_code} ({name}): {stock_error}")
                    failed_stocks.append({
                        'ts_code': ts_code,
                        'symbol': symbol,
                        'name': name,
                        'error': str(stock_error)
                    })
                    continue

        # Enhanced retry logic for failed stocks
        if failed_stocks:
            logger.info(f"\nRetrying {len(failed_stocks)} failed stocks...")
            retry_success_count = 0
            final_failed_stocks = []

            # Wait a bit before retrying to let rate limits reset
            logger.info("Waiting 30 seconds before retry attempts...")
            time.sleep(30)

            for i, stock_info in enumerate(failed_stocks):
                ts_code = stock_info['ts_code']
                symbol = stock_info['symbol']
                name = stock_info['name']

                logger.info(f"Retrying {i+1}/{len(failed_stocks)}: {ts_code} ({name})")

                try:
                    # Retry data fetch with enhanced error handling
                    df_daily = api_call_with_retry(
                        pro.daily,
                        ts_code=ts_code,
                        start_date=start_date,
                        end_date=end_date
                    )

                    if df_daily is None or df_daily.empty:
                        logger.warning(f"Still no daily data for {ts_code} ({name}) on retry")
                        final_failed_stocks.append(stock_info)
                        continue

                    df_basic = api_call_with_retry(
                        pro.daily_basic,
                        ts_code=ts_code,
                        start_date=start_date,
                        end_date=end_date,
                        fields='ts_code,trade_date,close,turnover_rate,turnover_rate_f,volume_ratio,pe,pe_ttm,pb,ps,ps_ttm,dv_ratio,dv_ttm,total_share,float_share,free_share,total_mv,circ_mv'
                    )

                    if df_basic is None:
                        df_basic = pd.DataFrame()

                    # Merge and process data
                    if not df_basic.empty:
                        df = pd.merge(df_daily, df_basic, on=['ts_code', 'trade_date'], how='left', suffixes=('', '_basic'))
                    else:
                        df = df_daily.copy()

                    # Add re-weighting columns
                    df['reweight_factor'] = reweight_factor
                    df['reweighted_close'] = df['close'] * df['reweight_factor']
                    df['reweighted_open'] = df['open'] * df['reweight_factor']
                    df['reweighted_high'] = df['high'] * df['reweight_factor']
                    df['reweighted_low'] = df['low'] * df['reweight_factor']

                    table_name = f'stock_daily_{symbol}'
                    df.to_sql(name=table_name, con=engine, if_exists='append', index=False)

                    # Update summary table for retry success
                    start_date_val = df['trade_date'].min() if not df.empty else start_date
                    end_date_val = df['trade_date'].max() if not df.empty else end_date
                    update_stock_summary(ts_code, symbol, name, table_name, len(df), start_date_val, end_date_val)

                    retry_success_count += 1
                    processed_count += 1
                    logger.info(f"Retry success {retry_success_count}/{len(failed_stocks)}: {ts_code} ({name})")

                except Exception as retry_error:
                    logger.error(f"Retry failed for {ts_code} ({name}): {retry_error}")
                    final_failed_stocks.append(stock_info)
                    continue

        # Final summary
        logger.info(f"\n{'='*50}")
        logger.info(f"FINAL RESULTS:")
        logger.info(f"Successfully processed: {processed_count} stocks")
        logger.info(f"Failed stocks: {len(failed_stocks) - retry_success_count if failed_stocks else 0}")
        logger.info(f"Total API requests made: {rate_limiter.request_count}")
        logger.info(f"{'='*50}")

        return processed_count

    except Exception as e:
        logger.error(f"Critical error in main function: {e}")
        return None

def save_failed_stocks_to_file(failed_stocks: List[Dict[str, Any]], filename: str = "failed_stocks.json"):
    """Save failed stocks to a JSON file for later retry"""
    if failed_stocks:
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(failed_stocks, f, ensure_ascii=False, indent=2)
        logger.info(f"Saved {len(failed_stocks)} failed stocks to {filename}")

def load_failed_stocks_from_file(filename: str = "failed_stocks.json") -> List[Dict[str, Any]]:
    """Load failed stocks from a JSON file"""
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            failed_stocks = json.load(f)
        logger.info(f"Loaded {len(failed_stocks)} failed stocks from {filename}")
        return failed_stocks
    except FileNotFoundError:
        logger.info(f"No failed stocks file found: {filename}")
        return []

def retry_failed_stocks_only(filename: str = "failed_stocks.json", start_date=None, end_date=None, reweight_factor=1.0):
    """Retry only the previously failed stocks"""
    failed_stocks = load_failed_stocks_from_file(filename)
    if not failed_stocks:
        logger.info("No failed stocks to retry")
        return 0

    logger.info(f"Retrying {len(failed_stocks)} failed stocks...")
    processed_count = 0

    for stock_info in failed_stocks:
        ts_code = stock_info['ts_code']
        symbol = stock_info['symbol']
        name = stock_info['name']

        try:
            # Use the same logic as the main function
            df_daily = api_call_with_retry(
                pro.daily,
                ts_code=ts_code,
                start_date=start_date,
                end_date=end_date
            )

            if df_daily is None or df_daily.empty:
                continue

            df_basic = api_call_with_retry(
                pro.daily_basic,
                ts_code=ts_code,
                start_date=start_date,
                end_date=end_date,
                fields='ts_code,trade_date,close,turnover_rate,turnover_rate_f,volume_ratio,pe,pe_ttm,pb,ps,ps_ttm,dv_ratio,dv_ttm,total_share,float_share,free_share,total_mv,circ_mv'
            )

            if df_basic is None:
                df_basic = pd.DataFrame()

            if not df_basic.empty:
                df = pd.merge(df_daily, df_basic, on=['ts_code', 'trade_date'], how='left', suffixes=('', '_basic'))
            else:
                df = df_daily.copy()

            # Add re-weighting columns
            df['reweight_factor'] = reweight_factor
            df['reweighted_close'] = df['close'] * df['reweight_factor']
            df['reweighted_open'] = df['open'] * df['reweight_factor']
            df['reweighted_high'] = df['high'] * df['reweight_factor']
            df['reweighted_low'] = df['low'] * df['reweight_factor']

            table_name = f'stock_daily_{symbol}'
            df.to_sql(name=table_name, con=engine, if_exists='append', index=False)

            # Update summary table
            start_date_val = df['trade_date'].min() if not df.empty else start_date
            end_date_val = df['trade_date'].max() if not df.empty else end_date
            update_stock_summary(ts_code, symbol, name, table_name, len(df), start_date_val, end_date_val)

            processed_count += 1
            logger.info(f"Retry success: {ts_code} ({name})")

        except Exception as e:
            logger.error(f"Retry failed for {ts_code} ({name}): {e}")
            continue

    logger.info(f"Successfully retried {processed_count} stocks")
    return processed_count

def show_database_statistics():
    """Show statistics about the database and stock tables"""
    try:
        with engine.connect() as connection:
            # Get summary statistics
            summary_stats = connection.execute(text("""
                SELECT
                    COUNT(*) as total_stocks,
                    SUM(record_count) as total_records,
                    MIN(start_date) as earliest_date,
                    MAX(end_date) as latest_date,
                    AVG(record_count) as avg_records_per_stock
                FROM stock_summary
            """)).fetchone()

            # Get top 10 stocks by record count
            top_stocks = connection.execute(text("""
                SELECT ts_code, name, record_count, start_date, end_date
                FROM stock_summary
                ORDER BY record_count DESC
                LIMIT 10
            """)).fetchall()

            # Get recent updates
            recent_updates = connection.execute(text("""
                SELECT ts_code, name, record_count, last_updated
                FROM stock_summary
                ORDER BY last_updated DESC
                LIMIT 5
            """)).fetchall()

            logger.info(f"\n{'='*60}")
            logger.info(f"DATABASE STATISTICS - '{DATABASE_NAME}'")
            logger.info(f"{'='*60}")

            if summary_stats:
                logger.info(f"Total Stocks: {summary_stats[0]:,}")
                logger.info(f"Total Records: {summary_stats[1]:,}")
                logger.info(f"Date Range: {summary_stats[2]} to {summary_stats[3]}")
                logger.info(f"Average Records per Stock: {summary_stats[4]:.1f}")

            logger.info(f"\nTop 10 Stocks by Record Count:")
            logger.info(f"{'Code':<12} {'Name':<20} {'Records':<10} {'Date Range'}")
            logger.info(f"{'-'*60}")
            for stock in top_stocks:
                logger.info(f"{stock[0]:<12} {stock[1][:20]:<20} {stock[2]:<10} {stock[3]} to {stock[4]}")

            logger.info(f"\nRecent Updates:")
            logger.info(f"{'Code':<12} {'Name':<20} {'Records':<10} {'Updated'}")
            logger.info(f"{'-'*60}")
            for stock in recent_updates:
                logger.info(f"{stock[0]:<12} {stock[1][:20]:<20} {stock[2]:<10} {stock[3]}")

            logger.info(f"{'='*60}")

    except Exception as e:
        logger.error(f"Error showing database statistics: {e}")

if __name__ == "__main__":
    # Fetch data for all stocks with improved error handling
    logger.info(f"Starting stock data fetch process...")
    logger.info(f"Target database: '{DATABASE_NAME}'")

    result = fetch_all_stocks_daily_data()

    if result is not None:
        logger.info(f"Process completed successfully. Processed {result} stocks.")

        # Show database statistics after completion
        show_database_statistics()

    else:
        logger.error("Process failed.")

    # Uncomment the line below to retry only failed stocks from a previous run
    # retry_failed_stocks_only()

    # Uncomment the line below to show current database statistics
    # show_database_statistics()




