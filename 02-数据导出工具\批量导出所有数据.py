#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
批量导出所有数据库数据
基于预览确认的格式，批量导出所有数据
"""

import pandas as pd
import pymysql
from pathlib import Path
import sys
import time
from datetime import datetime
import json

# 配置
EXPORT_DIR = Path(r"K:\数据库存储地")
MYSQL_CONFIG = {
    "host": "localhost",
    "port": 3306,
    "user": "root",
    "password": "12345678",
    "charset": "utf8mb4"
}

# 数据库列表
DATABASES = ["Daily Line", "index", "qtdb", "ind"]

def get_connection(database=None):
    """获取数据库连接"""
    try:
        config = MYSQL_CONFIG.copy()
        if database:
            config['database'] = database
        return pymysql.connect(**config)
    except Exception as e:
        print(f"数据库连接失败: {e}")
        return None

def get_table_info(database, table):
    """获取表信息"""
    try:
        conn = get_connection(database)
        if not conn:
            return None

        cursor = conn.cursor()
        cursor.execute(f"SELECT COUNT(*) FROM `{table}`")
        row_count = cursor.fetchone()[0]

        cursor.execute(f"DESCRIBE `{table}`")
        columns = cursor.fetchall()

        conn.close()
        return {
            "row_count": row_count,
            "column_count": len(columns),
            "columns": [col[0] for col in columns]
        }
    except Exception as e:
        print(f"获取表信息失败 {database}.{table}: {e}")
        return None

def get_date_column(database, table):
    """获取表的日期列名"""
    date_columns = {
        "Daily Line": "trade_date",
        "index": "candle_end_time",
        "ind": "transaction_date",
        "qtdb": "end_date"
    }
    return date_columns.get(database, None)

def export_table(database, table, chunk_size=50000):
    """导出单个表"""
    print(f"导出表: {database}.{table}")

    try:
        conn = get_connection(database)
        if not conn:
            return False

        # 获取表信息
        table_info = get_table_info(database, table)
        if not table_info:
            print(f"  无法获取表信息")
            return False

        row_count = table_info["row_count"]
        print(f"  行数: {row_count:,}")

        if row_count == 0:
            print(f"  表为空，跳过")
            return True

        # 创建数据库目录
        db_dir = EXPORT_DIR / database
        db_dir.mkdir(exist_ok=True)

        # 获取日期列名用于排序
        date_column = get_date_column(database, table)
        order_clause = ""
        if date_column:
            # 检查表中是否存在该日期列
            cursor = conn.cursor()
            cursor.execute(f"DESCRIBE `{table}`")
            columns = [col[0] for col in cursor.fetchall()]
            if date_column in columns:
                order_clause = f" ORDER BY `{date_column}` ASC"
                print(f"  按 {date_column} 从旧到新排序")
            else:
                print(f"  未找到日期列 {date_column}，使用默认排序")

        start_time = time.time()

        if row_count <= chunk_size:
            # 小表直接导出
            print(f"  直接导出...")
            query = f"SELECT * FROM `{table}`{order_clause}"
            df = pd.read_sql(query, conn)

            # 保存为CSV
            csv_file = db_dir / f"{table}.csv"
            df.to_csv(csv_file, index=False, encoding='utf-8-sig')

            # 如果数据量不大，也保存Excel格式
            if len(df) <= 100000:
                excel_file = db_dir / f"{table}.xlsx"
                df.to_excel(excel_file, index=False)
                print(f"  ✓ 已保存: {csv_file.name} 和 {excel_file.name}")
            else:
                print(f"  ✓ 已保存: {csv_file.name}")

        else:
            # 大表分块导出
            print(f"  分块导出 (每块 {chunk_size:,} 行)...")

            # 创建表目录
            table_dir = db_dir / table
            table_dir.mkdir(exist_ok=True)

            total_chunks = (row_count + chunk_size - 1) // chunk_size

            for i in range(0, row_count, chunk_size):
                chunk_num = i // chunk_size + 1
                print(f"    分块 {chunk_num}/{total_chunks}")

                # 添加排序和分页
                query = f"SELECT * FROM `{table}`{order_clause} LIMIT {chunk_size} OFFSET {i}"
                df = pd.read_sql(query, conn)

                if not df.empty:
                    chunk_file = table_dir / f"{table}_part_{chunk_num:04d}.csv"
                    df.to_csv(chunk_file, index=False, encoding='utf-8-sig')

            print(f"  ✓ 已保存到目录: {table_dir.name}")

        duration = time.time() - start_time
        print(f"  耗时: {duration:.1f}秒")

        conn.close()
        return True

    except Exception as e:
        print(f"  ✗ 导出失败: {e}")
        return False

def export_database(database):
    """导出整个数据库"""
    print(f"\n{'='*60}")
    print(f"导出数据库: {database}")
    print(f"{'='*60}")

    try:
        conn = get_connection(database)
        if not conn:
            print(f"无法连接到数据库 {database}")
            return 0, 0

        cursor = conn.cursor()
        cursor.execute("SHOW TABLES")
        tables = [table[0] for table in cursor.fetchall()]
        conn.close()

        if not tables:
            print(f"数据库 {database} 没有表")
            return 0, 0

        print(f"找到 {len(tables)} 个表")

        success_count = 0
        for i, table in enumerate(tables, 1):
            print(f"\n[{i}/{len(tables)}] ", end="")
            if export_table(database, table):
                success_count += 1

        print(f"\n数据库 {database} 导出完成: {success_count}/{len(tables)} 成功")
        return success_count, len(tables)

    except Exception as e:
        print(f"导出数据库 {database} 时出错: {e}")
        return 0, 0

def generate_export_summary():
    """生成导出汇总报告"""
    print(f"\n生成导出汇总报告...")

    summary = {
        "export_time": datetime.now().isoformat(),
        "export_directory": str(EXPORT_DIR),
        "databases": {}
    }

    total_files = 0
    total_size = 0

    for database in DATABASES:
        db_dir = EXPORT_DIR / database
        if not db_dir.exists():
            continue

        db_info = {
            "tables": {},
            "total_files": 0,
            "total_size": 0
        }

        # 统计文件
        for file_path in db_dir.rglob("*.csv"):
            size = file_path.stat().st_size
            db_info["total_files"] += 1
            db_info["total_size"] += size
            total_files += 1
            total_size += size

            # 记录表信息
            if file_path.parent == db_dir:
                # 直接在数据库目录下的文件
                table_name = file_path.stem
                db_info["tables"][table_name] = {
                    "file": file_path.name,
                    "size": size
                }
            else:
                # 分块文件
                table_name = file_path.parent.name
                if table_name not in db_info["tables"]:
                    db_info["tables"][table_name] = {
                        "type": "chunked",
                        "files": [],
                        "total_size": 0
                    }
                db_info["tables"][table_name]["files"].append(file_path.name)
                db_info["tables"][table_name]["total_size"] += size

        summary["databases"][database] = db_info

    summary["total_files"] = total_files
    summary["total_size"] = total_size

    # 保存汇总报告
    report_file = EXPORT_DIR / "导出汇总报告.json"
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(summary, f, ensure_ascii=False, indent=2)

    print(f"汇总报告已保存: {report_file}")

    # 打印汇总信息
    print(f"\n{'='*60}")
    print(f"导出汇总")
    print(f"{'='*60}")
    print(f"总文件数: {total_files:,}")
    print(f"总大小: {total_size/1024/1024:.1f} MB")
    print(f"导出目录: {EXPORT_DIR}")

    for database, info in summary["databases"].items():
        if info["total_files"] > 0:
            print(f"\n{database}:")
            print(f"  文件数: {info['total_files']:,}")
            print(f"  大小: {info['total_size']/1024/1024:.1f} MB")
            print(f"  表数: {len(info['tables'])}")

def main():
    """主函数"""
    print("="*60)
    print("批量导出所有数据库数据")
    print("="*60)

    # 确认导出
    print(f"导出目录: {EXPORT_DIR}")
    print(f"目标数据库: {', '.join(DATABASES)}")
    print(f"\n注意: 这个过程可能需要很长时间，请确保:")
    print(f"1. 有足够的磁盘空间")
    print(f"2. 数据库连接稳定")
    print(f"3. 不要中断程序执行")

    confirm = input(f"\n确认开始批量导出吗? (y/N): ").strip().lower()
    if confirm != 'y':
        print("已取消导出")
        return

    # 创建导出目录
    EXPORT_DIR.mkdir(parents=True, exist_ok=True)

    start_time = datetime.now()
    print(f"\n开始时间: {start_time.strftime('%Y-%m-%d %H:%M:%S')}")

    total_success = 0
    total_tables = 0

    # 导出每个数据库
    for database in DATABASES:
        success, tables = export_database(database)
        total_success += success
        total_tables += tables

    # 生成汇总报告
    generate_export_summary()

    end_time = datetime.now()
    duration = end_time - start_time

    print(f"\n{'='*60}")
    print(f"导出完成")
    print(f"{'='*60}")
    print(f"开始时间: {start_time.strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"结束时间: {end_time.strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"总耗时: {duration}")
    print(f"成功导出: {total_success}/{total_tables} 个表")

    if total_success == total_tables:
        print("🎉 所有数据导出成功！")
    else:
        print(f"⚠️ 部分数据导出失败")

if __name__ == "__main__":
    main()
    input("\n按回车键退出...")
# -*- coding: utf-8 -*-
"""
批量导出所有数据库数据
基于预览确认的格式，批量导出所有数据
"""

import pandas as pd
import pymysql
from pathlib import Path
import sys
import time
from datetime import datetime
import json

# 配置
EXPORT_DIR = Path(r"K:\数据库存储地")
MYSQL_CONFIG = {
    "host": "localhost",
    "port": 3306,
    "user": "root",
    "password": "12345678",
    "charset": "utf8mb4"
}

# 数据库列表
DATABASES = ["Daily Line", "index", "qtdb", "ind"]

def get_connection(database=None):
    """获取数据库连接"""
    try:
        config = MYSQL_CONFIG.copy()
        if database:
            config['database'] = database
        return pymysql.connect(**config)
    except Exception as e:
        print(f"数据库连接失败: {e}")
        return None

def get_table_info(database, table):
    """获取表信息"""
    try:
        conn = get_connection(database)
        if not conn:
            return None
        
        cursor = conn.cursor()
        cursor.execute(f"SELECT COUNT(*) FROM `{table}`")
        row_count = cursor.fetchone()[0]
        
        cursor.execute(f"DESCRIBE `{table}`")
        columns = cursor.fetchall()
        
        conn.close()
        return {
            "row_count": row_count,
            "column_count": len(columns),
            "columns": [col[0] for col in columns]
        }
    except Exception as e:
        print(f"获取表信息失败 {database}.{table}: {e}")
        return None

def get_date_column(database, table):
    """获取表的日期列名"""
    date_columns = {
        "Daily Line": "trade_date",
        "index": "candle_end_time",
        "ind": "transaction_date",
        "qtdb": "end_date"  # 财务数据使用end_date作为主要日期
    }
    return date_columns.get(database, None)

def export_table(database, table, chunk_size=50000):
    """导出单个表"""
    print(f"导出表: {database}.{table}")

    try:
        conn = get_connection(database)
        if not conn:
            return False

        # 获取表信息
        table_info = get_table_info(database, table)
        if not table_info:
            print(f"  无法获取表信息")
            return False

        row_count = table_info["row_count"]
        print(f"  行数: {row_count:,}")

        if row_count == 0:
            print(f"  表为空，跳过")
            return True

        # 创建数据库目录
        db_dir = EXPORT_DIR / database
        db_dir.mkdir(exist_ok=True)

        # 获取日期列名用于排序
        date_column = get_date_column(database, table)
        order_clause = ""
        if date_column:
            # 检查表中是否存在该日期列
            cursor = conn.cursor()
            cursor.execute(f"DESCRIBE `{table}`")
            columns = [col[0] for col in cursor.fetchall()]
            if date_column in columns:
                order_clause = f" ORDER BY `{date_column}` ASC"
                print(f"  按 {date_column} 从旧到新排序")
            else:
                print(f"  未找到日期列 {date_column}，使用默认排序")

        start_time = time.time()

        if row_count <= chunk_size:
            # 小表直接导出
            print(f"  直接导出...")
            query = f"SELECT * FROM `{table}`{order_clause}"
            df = pd.read_sql(query, conn)
            
            # 保存为CSV
            csv_file = db_dir / f"{table}.csv"
            df.to_csv(csv_file, index=False, encoding='utf-8-sig')
            
            # 如果数据量不大，也保存Excel格式
            if len(df) <= 100000:
                excel_file = db_dir / f"{table}.xlsx"
                df.to_excel(excel_file, index=False)
                print(f"  ✓ 已保存: {csv_file.name} 和 {excel_file.name}")
            else:
                print(f"  ✓ 已保存: {csv_file.name}")
        
        else:
            # 大表分块导出
            print(f"  分块导出 (每块 {chunk_size:,} 行)...")

            # 创建表目录
            table_dir = db_dir / table
            table_dir.mkdir(exist_ok=True)

            total_chunks = (row_count + chunk_size - 1) // chunk_size

            for i in range(0, row_count, chunk_size):
                chunk_num = i // chunk_size + 1
                print(f"    分块 {chunk_num}/{total_chunks}")

                # 添加排序和分页
                query = f"SELECT * FROM `{table}`{order_clause} LIMIT {chunk_size} OFFSET {i}"
                df = pd.read_sql(query, conn)

                if not df.empty:
                    chunk_file = table_dir / f"{table}_part_{chunk_num:04d}.csv"
                    df.to_csv(chunk_file, index=False, encoding='utf-8-sig')

            print(f"  ✓ 已保存到目录: {table_dir.name}")
        
        duration = time.time() - start_time
        print(f"  耗时: {duration:.1f}秒")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"  ✗ 导出失败: {e}")
        return False

def export_database(database):
    """导出整个数据库"""
    print(f"\n{'='*60}")
    print(f"导出数据库: {database}")
    print(f"{'='*60}")
    
    try:
        conn = get_connection(database)
        if not conn:
            print(f"无法连接到数据库 {database}")
            return 0, 0
        
        cursor = conn.cursor()
        cursor.execute("SHOW TABLES")
        tables = [table[0] for table in cursor.fetchall()]
        conn.close()
        
        if not tables:
            print(f"数据库 {database} 没有表")
            return 0, 0
        
        print(f"找到 {len(tables)} 个表")
        
        success_count = 0
        for i, table in enumerate(tables, 1):
            print(f"\n[{i}/{len(tables)}] ", end="")
            if export_table(database, table):
                success_count += 1
        
        print(f"\n数据库 {database} 导出完成: {success_count}/{len(tables)} 成功")
        return success_count, len(tables)
        
    except Exception as e:
        print(f"导出数据库 {database} 时出错: {e}")
        return 0, 0

def generate_export_summary():
    """生成导出汇总报告"""
    print(f"\n生成导出汇总报告...")
    
    summary = {
        "export_time": datetime.now().isoformat(),
        "export_directory": str(EXPORT_DIR),
        "databases": {}
    }
    
    total_files = 0
    total_size = 0
    
    for database in DATABASES:
        db_dir = EXPORT_DIR / database
        if not db_dir.exists():
            continue
        
        db_info = {
            "tables": {},
            "total_files": 0,
            "total_size": 0
        }
        
        # 统计文件
        for file_path in db_dir.rglob("*.csv"):
            size = file_path.stat().st_size
            db_info["total_files"] += 1
            db_info["total_size"] += size
            total_files += 1
            total_size += size
            
            # 记录表信息
            if file_path.parent == db_dir:
                # 直接在数据库目录下的文件
                table_name = file_path.stem
                db_info["tables"][table_name] = {
                    "file": file_path.name,
                    "size": size
                }
            else:
                # 分块文件
                table_name = file_path.parent.name
                if table_name not in db_info["tables"]:
                    db_info["tables"][table_name] = {
                        "type": "chunked",
                        "files": [],
                        "total_size": 0
                    }
                db_info["tables"][table_name]["files"].append(file_path.name)
                db_info["tables"][table_name]["total_size"] += size
        
        summary["databases"][database] = db_info
    
    summary["total_files"] = total_files
    summary["total_size"] = total_size
    
    # 保存汇总报告
    report_file = EXPORT_DIR / "导出汇总报告.json"
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(summary, f, ensure_ascii=False, indent=2)
    
    print(f"汇总报告已保存: {report_file}")
    
    # 打印汇总信息
    print(f"\n{'='*60}")
    print(f"导出汇总")
    print(f"{'='*60}")
    print(f"总文件数: {total_files:,}")
    print(f"总大小: {total_size/1024/1024:.1f} MB")
    print(f"导出目录: {EXPORT_DIR}")
    
    for database, info in summary["databases"].items():
        if info["total_files"] > 0:
            print(f"\n{database}:")
            print(f"  文件数: {info['total_files']:,}")
            print(f"  大小: {info['total_size']/1024/1024:.1f} MB")
            print(f"  表数: {len(info['tables'])}")

def main():
    """主函数"""
    print("="*60)
    print("批量导出所有数据库数据")
    print("="*60)
    
    # 确认导出
    print(f"导出目录: {EXPORT_DIR}")
    print(f"目标数据库: {', '.join(DATABASES)}")
    print(f"\n注意: 这个过程可能需要很长时间，请确保:")
    print(f"1. 有足够的磁盘空间")
    print(f"2. 数据库连接稳定")
    print(f"3. 不要中断程序执行")
    
    confirm = input(f"\n确认开始批量导出吗? (y/N): ").strip().lower()
    if confirm != 'y':
        print("已取消导出")
        return
    
    # 创建导出目录
    EXPORT_DIR.mkdir(parents=True, exist_ok=True)
    
    start_time = datetime.now()
    print(f"\n开始时间: {start_time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    total_success = 0
    total_tables = 0
    
    # 导出每个数据库
    for database in DATABASES:
        success, tables = export_database(database)
        total_success += success
        total_tables += tables
    
    # 生成汇总报告
    generate_export_summary()
    
    end_time = datetime.now()
    duration = end_time - start_time
    
    print(f"\n{'='*60}")
    print(f"导出完成")
    print(f"{'='*60}")
    print(f"开始时间: {start_time.strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"结束时间: {end_time.strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"总耗时: {duration}")
    print(f"成功导出: {total_success}/{total_tables} 个表")
    
    if total_success == total_tables:
        print("🎉 所有数据导出成功！")
    else:
        print(f"⚠️ 部分数据导出失败")

if __name__ == "__main__":
    main()
    input("\n按回车键退出...")
